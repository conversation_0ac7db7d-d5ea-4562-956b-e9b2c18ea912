import { Modu<PERSON> } from '@nestjs/common';
import { StoreService } from './store.service';
import { StoreController } from './store.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Store } from './entities/store.entity';
import { AutomapperModule } from '@automapper/nestjs';
import { StoreMapperService } from './store.mapper.service';
import { StoreValidatorService } from './store.validator.service';
import { LoggerModule } from '@common/logger/logger.module';
import { ItemUnitModule } from './item-unit/item-unit.module';

@Module({
  controllers: [StoreController],
  exports: [StoreService, StoreValidatorService],
  imports: [
    TypeOrmModule.forFeature([Store]),
    AutomapperModule,
    LoggerModule,
    ItemUnitModule,
  ],
  providers: [StoreService, StoreMapperService, StoreValidatorService],
})
export class StoreModule {}
