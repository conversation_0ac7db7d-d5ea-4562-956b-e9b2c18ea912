import { AutoMap } from '@automapper/classes';
import { IsEmail } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateUserDto {
  @AutoMap()
  @IsEmail()
  @ApiProperty()
  email: string;

  @AutoMap()
  @ApiProperty()
  firstName?: string;

  @AutoMap()
  @ApiProperty()
  roleId?: number;

  @AutoMap()
  @ApiProperty()
  lastName?: string;

  @AutoMap()
  @ApiProperty()
  phoneNumber?: string;

  @AutoMap()
  @ApiProperty()
  password: string;

  @AutoMap()
  @ApiProperty()
  departmentId: number;
}
