import { AutoMap } from '@automapper/classes';
import { Column, Entity } from 'typeorm';
import { AbstractEntity } from '../../entities/base.entity';

@Entity({ name: 'settings' })
export class Settings extends AbstractEntity {
  @AutoMap()
  @Column({ name: 'key' })
  key: string;

  @AutoMap()
  @Column({ name: 'value' })
  value: string;

  @AutoMap()
  @Column({ name: 'owner_id' })
  ownerId: number;
}
