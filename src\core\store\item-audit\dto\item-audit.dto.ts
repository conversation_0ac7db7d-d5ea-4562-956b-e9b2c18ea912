import { EntityDto } from '@common/dto/base.dto';
import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { Condition } from '@common/enums/condition.enum';

export class ItemAuditDto extends EntityDto {
  @AutoMap()
  @ApiProperty()
  itemId: number;

  @AutoMap()
  @ApiProperty()
  itemName: string;

  @AutoMap()
  @ApiProperty()
  quantityLeased: number;

  @AutoMap()
  @ApiProperty()
  leasedDate: Date;

  @AutoMap(() => String)
  @ApiProperty({
    type: 'string',
    enum: Object.values(Condition),
    name: 'conditionBeforeLease',
    description: 'Possible item conditions',
  })
  conditionBeforeLease: Condition;

  @AutoMap()
  @ApiProperty()
  quantityReturned: number;

  @AutoMap()
  @ApiProperty()
  returnedDate: Date;

  @AutoMap()
  @ApiProperty()
  quantityReleased: number;

  @AutoMap()
  @ApiProperty()
  releasedDate: Date;

  @AutoMap(() => String)
  @ApiProperty({
    type: 'string',
    enum: Object.values(Condition),
    name: 'conditionAfterReturned',
    description: 'Possible item conditions',
  })
  conditionAfterReturned: Condition;

  @AutoMap()
  @ApiProperty()
  audit_completed: boolean;

  @AutoMap()
  @ApiProperty()
  auditCompletionDate: Date;

  @AutoMap()
  @ApiProperty()
  audit_collected: boolean;

  @AutoMap()
  @ApiProperty()
  auditCollectionDate: Date;
}
