import { BaseValidator } from '@common/validator/base.validator';
import { Injectable, NotFoundException } from '@nestjs/common';
import { DatabaseAction } from '@common/enums/dbaction.enum';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { Store } from '@core/store/entities/store.entity';

@Injectable()
export class StoreValidatorService implements BaseValidator<Store> {
  constructor(
    @InjectRepository(Store) private storeRepository: Repository<Store>,
  ) {}

  async validate(data: Store, action: DatabaseAction) {
    const existing: Store = await this.storeRepository.findOne({
      where: { id: data?.id },
    });

    if (DatabaseAction.CREATE === action) {
      // throw new Error('Prayer already exists');
      return;
    } else if (
      existing &&
      existing?.id !== data?.id &&
      DatabaseAction.UPDATE === action
    ) {
      throw new NotFoundException('Store not found.');
    }
  }

  async isStoreExists(storeId: number): Promise<boolean> {
    const existing = await this.storeRepository.findOne({
      where: { id: storeId },
    });
    return !!existing;
  }
}
