import { EntityDto } from '../../../common/dto/base.dto';
import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { StoreAddressDto } from '../../security/address/dto/store-address.dto';

export class StoreDto extends EntityDto {
  @AutoMap()
  @ApiProperty()
  name: string;

  @AutoMap()
  @ApiProperty({ type: StoreAddressDto })
  location: StoreAddressDto;
}
