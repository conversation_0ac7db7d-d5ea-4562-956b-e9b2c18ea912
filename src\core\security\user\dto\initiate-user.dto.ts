import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNumber, IsOptional, IsString } from 'class-validator';

export class InitiateUserDto {
  @AutoMap()
  @ApiProperty()
  @IsEmail()
  email: string;

  @AutoMap()
  @ApiProperty()
  @IsString()
  firstName: string;

  @AutoMap()
  @ApiProperty()
  @IsString()
  lastName: string;

  @AutoMap()
  @ApiProperty()
  @IsNumber()
  role: number;

  @AutoMap()
  @ApiProperty()
  @IsString()
  phoneNumber: string;

  @AutoMap()
  @ApiProperty()
  @IsNumber()
  @IsOptional()
  departmentId: number;
}
