.env
.env copy
.env.copy.dummy
.env.heroku
.env.example.2
bitbucket-pipelines.yml
composer copy.json
composer.lock copy
composer.lock copy 2
heroku-config.py
Procfile
.gitlab-ci.yml

# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Source control
.git
.gitignore
.gitattributes
.github

# IDE and Editor files
.idea
.vscode
*.swp
*.swo
.DS_Store
*.sublime-*

# Test files
test
coverage
.nyc_output
*.spec.ts
*.test.ts

# Development configs
.env
.env.*
!.env.example
.editorconfig
.eslintrc
.prettierrc

# Build and dist directories (if you're using multi-stage builds)
# dist
# build

# Documentation
docs
*.md
CHANGELOG
LICENSE

# Temporary files
tmp
temp
*.log
logs

# Other unnecessary files
.husky
.npmrc
.yarnrc
*.tgz
*.tar.gz