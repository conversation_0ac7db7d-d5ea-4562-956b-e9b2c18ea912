import { config } from 'dotenv';

config();

export interface StorageOptions {
  s3?: AwsS3ConnectionOptions;
  gcs?: GcpStorageOptions;
  cloudinary?: CloudinaryStorageOption;
}

export interface AwsS3ConnectionOptions {
  bucket: string;
  region: string;
  credentials: AwsCredentials;
}

export interface AwsCredentials {
  accessKey: string;
  secretKey: string;
}

export interface GcpStorageOptions {
  bucket: string;
  credentialsJson: string;
}

export interface CloudinaryStorageOption {
  cloudName: string;
  key: string;
  secret: string;
}

export const s3Config = (): AwsS3ConnectionOptions => ({
  bucket: process.env.AWS_BUCKET,
  region: process.env.AWS_REGION,
  credentials: {
    accessKey: process.env.AWS_ACCESS_KEY,
    secretKey: process.env.AWS_SECRET_KEY,
  },
});

export const gcsConfig = (): GcpStorageOptions => ({
  bucket: process.env.GCS_BUCKET,
  credentialsJson: process.env.GCS_CREDENTIALS,
});

export const cloudinaryConfig = (): CloudinaryStorageOption => ({
  cloudName: process.env.CLOUDINARY_CLOUD_NAME,
  key: process.env.CLOUDINARY_API_KEY,
  secret: process.env.CLOUDINARY_API_SECRET,
});
