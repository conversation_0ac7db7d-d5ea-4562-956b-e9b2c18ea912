{"name": "egfm-logistics-inventory-system-api", "version": "0.0.1", "description": "", "author": "<PERSON>", "private": true, "license": "MIT", "engines": {"node": "22.x", "npm": "9.x"}, "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "node ./node_modules/@nestjs/cli/bin/nest.js build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli", "typeorm:run-migrations": "npm run typeorm migration:run -- -d ./src/config/datasource.ts", "typeorm:generate-migration": "cross-var npm run typeorm -- -d ./src/config/datasource.ts migration:generate ./src/database/migrations/%npm_config_name%", "typeorm:create-migration": "cross-var npm run typeorm -- migration:create ./src/database/migrations/%npm_config_name%", "typeorm:revert-migration": "npm run typeorm -- -d ./src/config/datasource.ts migration:revert", "drop:database": "npm run typeorm -- -d ./src/config/datasource.ts schema:drop", "db:seed": "ts-node -r tsconfig-paths/register ./src/seed.ts"}, "dependencies": {"@automapper/classes": "^8.7.7", "@automapper/core": "^8.7.7", "@automapper/nestjs": "^8.7.7", "@casl/ability": "^6.7.1", "@nestjs/axios": "^1.0.1", "@nestjs/cli": "^9.0.0", "@nestjs/common": "^9.0.0", "@nestjs/config": "^2.2.0", "@nestjs/core": "^9.0.0", "@nestjs/jwt": "^9.0.0", "@nestjs/mapped-types": "*", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^9.0.0", "@nestjs/schedule": "^4.0.2", "@nestjs/swagger": "^7.3.1", "@nestjs/throttler": "^6.1.0", "@nestjs/typeorm": "^10.0.2", "@types/express": "^4.17.13", "@types/jest": "^28.1.8", "@types/multer": "^1.4.11", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "class-transformer": "^0.5.1", "class-validator": "0.14.0", "cloudinary": "^2.2.0", "cross-var": "^1.1.0", "D": "^1.0.0", "dynamic-string": "^0.1.2", "lodash": "^4.17.21", "luxon": "^3.4.4", "mysql2": "3.9.4", "nestjs-i18n": "^10.2.1", "nestjs-mailer": "^1.0.1", "nestjs-paginate": "8.1.0", "nestjs-typeorm-paginate": "^4.0.3", "nodemailer": "^6.9.14", "passport": "^0.6.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.0", "pg": "^8.12.0", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.2.0", "socket.io": "^4.6.1", "twilio": "^5.2.2", "typeorm": "^0.3.20", "uuid": "^10.0.0"}, "devDependencies": {"@nestjs/schematics": "^9.0.0", "@nestjs/testing": "^9.0.0", "@types/bcryptjs": "^2.4.6", "@types/cron": "^2.4.0", "@types/lodash": "^4.17.6", "@types/node": "^16.0.0", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^2.0.11", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "fork-ts-checker-webpack-plugin": "^9.0.2", "jest": "28.1.3", "nodemon": "^3.1.9", "prettier": "^2.3.2", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "28.0.8", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "4.1.0", "typescript": "^4.7.4", "webpack": "^5.93.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}