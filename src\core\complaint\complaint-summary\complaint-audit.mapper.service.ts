import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import { createMap, Mapper, MappingProfile } from '@automapper/core';
import { ComplaintSummary } from './entities/complaint-summary.entity';
import { ComplaintSummaryDto } from './dto/complaint-summary.dto';

@Injectable()
export class ComplaintAuditMapperService extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile(): MappingProfile {
    return (mapper) => {
      createMap(mapper, ComplaintSummary, ComplaintSummaryDto);
      createMap(mapper, ComplaintSummaryDto, ComplaintSummary);
    };
  }
}
