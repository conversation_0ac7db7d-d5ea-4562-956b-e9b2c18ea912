import { Injectable } from '@nestjs/common';
import { InjectMailer, Mailer } from 'nestjs-mailer';
import { ConfigService } from '@nestjs/config';
import { LoggerService } from '../../../common/logger/logger.service';

@Injectable()
export class MailService {
  from: string;

  constructor(
    @InjectMailer() private readonly mailer: Mailer,
    private readonly configService: ConfigService,
    private readonly logger: LoggerService,
  ) {
    this.logger.setContext(MailService.name);
    this.from = this.configService.get<string>('defaults.from');
  }

  async sendSmtpMail(recipient: string, subject: string, message: string) {
    try {
      await this.mailer.sendMail({
        to: recipient,
        from: `"EGFM Logistics Team" <${this.from}>`,
        subject: subject,
        text: message,
      });
      this.logger.log(`Mail sent to ${recipient}`);
    } catch (err) {
      this.logger.error(`Error sending mail: \n${err?.message}`);
      throw new Error(err?.message);
    }
  }
}
