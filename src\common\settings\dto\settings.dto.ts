import { AutoMap } from "@automapper/classes";
import {IsNotEmpty, IsOptional, IsString} from "class-validator";
import {EntityDto} from "../../../common/dto/base.dto";

export class SettingsDto extends EntityDto{

    @AutoMap()
    @IsNotEmpty()
    @IsString()
    key: string;

    @AutoMap()
    @IsNotEmpty()
    @IsString()
    value?: string;

    @AutoMap()
    ownerId: number;

    @AutoMap()
    @IsOptional()
    ownerType: string;
}
