import { Request } from 'express';
import { DataSource, EntityManager, Repository } from 'typeorm';
import { CoreConstants } from '../utils/core.constants';

export class BaseRepository {
  constructor(private dataSource: DataSource, private request: Request) {}

  protected getRepository<T>(entity: new () => T): Repository<T> {
    const entityManager: EntityManager =
      this.request[CoreConstants.ENTITY_MANAGER_KEY] ?? this.dataSource.manager;
    return entityManager.getRepository(entity);
  }
}
