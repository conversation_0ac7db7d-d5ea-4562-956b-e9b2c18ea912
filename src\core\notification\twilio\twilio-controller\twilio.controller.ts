import { Body, Controller, HttpStatus, Post, Res } from '@nestjs/common';
import { TwilioMessage, TwilioService } from '../twilio.service';

@Controller('twilio')
export class TwilioController {
  constructor(private readonly twilioService: TwilioService) {}

  @Post('webhook')
  async handleIncomingMessage(
    @Body() body: TwilioMessage,
    @Res() response: any,
  ): Promise<void> {
    // Use the strategy here
    await this.twilioService.handleIncomingMessage(body);
    response.status(HttpStatus.OK).send('Message received');
  }
}
