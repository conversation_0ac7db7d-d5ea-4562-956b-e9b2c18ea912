import { Modu<PERSON> } from '@nestjs/common';
import { MaintenanceLogService } from './maintenance-log.service';
import { MaintenanceLogController } from './maintenance-log.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MaintenanceLog } from './entities/maintenance-log.entity';
import { AutomapperModule } from '@automapper/nestjs';
import { MaintenanceLogMapperService } from './maintenance-log.mapper.service';
import { MaintenanceLogValidator } from './maintenance-log.validator';
import { LoggerModule } from '@common/logger/logger.module';


@Module({
  controllers: [MaintenanceLogController],
  exports: [MaintenanceLogService],
  imports: [
    TypeOrmModule.forFeature([MaintenanceLog]),
    AutomapperModule,
    LoggerModule,
  ],
  providers: [
    MaintenanceLogService,
    MaintenanceLogMapperService,
    MaintenanceLogValidator,
  ],
})
export class MaintenanceLogModule {}
