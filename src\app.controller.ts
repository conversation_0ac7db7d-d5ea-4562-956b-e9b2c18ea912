import { Controller, Get, Head, Version } from '@nestjs/common';
import { AppService } from './app.service';
import { ApiOperation, ApiTags } from '@nestjs/swagger';

@ApiTags('Health Check')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  @Head()
  @Version('1')
  @ApiOperation({ summary: 'Health check endpoint' })
  healthCheck() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'EGFM Logistics Inventory System API',
    };
  }

  @Get('api')
  @Head('api')
  @Version('1')
  @ApiOperation({ summary: 'Health check endpoint with api prefix' })
  healthCheckWithPrefix() {
    return this.healthCheck();
  }
}
