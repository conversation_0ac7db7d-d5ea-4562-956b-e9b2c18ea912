import { Module } from '@nestjs/common';
import { SettingsService } from './settings.service';
import { SettingsController } from './settings.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Settings } from './entities/settings.entity';
import { AutomapperModule } from '@automapper/nestjs';
import { UserModule } from '../../core/security/user/user.module';

@Module({
  imports: [AutomapperModule, TypeOrmModule.forFeature([Settings]), UserModule],
  exports: [SettingsService],
  controllers: [SettingsController],
  providers: [SettingsService],
})
export class SettingsModule {}
