import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateMaintenanceLogTable1752616911393
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create Maintenance Log Table.
    await queryRunner.createTable(
      new Table({
        name: 'maintenance_log',
        columns: [
          {
            name: 'id',
            type: 'bigint',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'maintenance_date',
            type: 'timestamp',
          },
          {
            name: 'description',
            type: 'text',
          },
          {
            name: 'cost_of_maintenance',
            type: 'bigint',
          },
          {
            name: 'serviced_item',
            type: 'varchar',
          },
          {
            name: 'artisan_name',
            type: 'varchar',
          },
          {
            name: 'artisan_phone',
            type: 'varchar',
          },
          {
            name: 'signature',
            type: 'varchar',
          },
          {
            name: 'status',
            type: 'varchar',
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'now()',
          },
          {
            name: 'created_by',
            type: 'varchar',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'updated_by',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'deleted_at',
            type: 'timestamp',
            isNullable: true,
          },
        ],
      }),
      true,
    );

    // Create Maintenance Log Table Indexes.
    await queryRunner.createIndex(
      'maintenance_log',
      new TableIndex({
        name: 'IDX_MAINTENANCE_LOG_ARTISAN_NAME',
        columnNames: ['artisan_name'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop maintenance log index and table.
    await queryRunner.dropIndex(
      'maintenance_log',
      'IDX_MAINTENANCE_LOG_ARTISAN_NAME',
    );
    await queryRunner.dropTable('maintenance_log');
  }
}
