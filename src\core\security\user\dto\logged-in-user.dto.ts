import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { Gender } from '@common/enums/gender.enum';
import { LoggedInUserDepartmentDto } from '@core/security/department/dto/logged-in-user-department.dto';
import { LoggedInUserRoleDto } from '@core/security/role/dto/logged-in-user-role.dto';
import { LoggedInUserAddressDto } from '@core/security/address/dto/logged-in-user-address.dto';

export class LoggedInUserDto {
  @AutoMap()
  @ApiProperty()
  id: number;

  @AutoMap()
  @ApiProperty()
  email: string;

  @AutoMap()
  @ApiProperty()
  firstName?: string;

  @AutoMap()
  @ApiProperty()
  lastName?: string;

  @AutoMap()
  @ApiProperty({
    type: 'string',
    enum: Object.values(Gender),
    name: 'gender',
    description: 'Possible genders',
  })
  gender: Gender;

  @AutoMap()
  @ApiProperty()
  department: LoggedInUserDepartmentDto;

  @AutoMap()
  @ApiProperty()
  role: LoggedInUserRoleDto;

  @AutoMap()
  @ApiProperty()
  address?: LoggedInUserAddressDto;
}
