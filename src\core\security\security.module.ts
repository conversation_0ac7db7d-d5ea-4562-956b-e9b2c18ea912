import { Module } from '@nestjs/common';
import { UserModule } from './user/user.module';
import { RoleModule } from './role/role.module';
import { PermissionModule } from './permission/permission.module';
import { AuthenticationModule } from './authentication/authentication.module';
import { AddressModule } from './address/address.module';
import { AuthorizationModule } from './authorization/authorization.module';

@Module({
  imports: [
    UserModule,
    RoleModule,
    PermissionModule,
    AuthenticationModule,
    AddressModule,
    AuthorizationModule,
  ],
  exports: [UserModule],
})
export class SecurityModule {}
