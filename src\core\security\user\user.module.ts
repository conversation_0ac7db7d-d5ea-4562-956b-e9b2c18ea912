import { Module } from '@nestjs/common';
import { UserService } from './user.service';
import { UserController } from './user.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from './entities/user.entity';
import { AutomapperModule } from '@automapper/nestjs';
import { UserValidator } from './user.validator';
import { UserProfile } from './user.profile';
import { LoggerModule } from '@common/logger/logger.module';
import { NotificationModule } from '../../notification/notification.module';
import { RoleModule } from '../role/role.module';
import { AuthorizationModule } from '../authorization/authorization.module';
import { DepartmentModule } from '@core/security/department/department.module';

@Module({
  controllers: [UserController],
  exports: [UserService],
  imports: [
    TypeOrmModule.forFeature([User]),
    AutomapperModule,
    LoggerModule,
    NotificationModule,
    DepartmentModule,
    RoleModule,
    AuthorizationModule,
  ],
  providers: [UserService, UserValidator, UserProfile],
})
export class UserModule {}
