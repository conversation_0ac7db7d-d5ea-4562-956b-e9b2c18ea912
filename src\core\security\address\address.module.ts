import { Module } from '@nestjs/common';
import { AddressService } from './address.service';
import { AddressController } from './address.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Address } from './entities/address.entity';
import { AutomapperModule } from '@automapper/nestjs';
import { AddressProfile } from './address.profile';

@Module({
  imports: [TypeOrmModule.forFeature([Address]), AutomapperModule],
  controllers: [AddressController],
  providers: [AddressService, AddressProfile],
  exports: [AddressService],
})
export class AddressModule {}
