import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { EntityDto } from '@common/dto/base.dto';

export class GeneratorLogDto extends EntityDto {
  @AutoMap()
  @ApiProperty()
  nameOfMeeting: string;

  @AutoMap()
  @ApiProperty()
  generatorType: string;

  @AutoMap()
  @ApiProperty()
  meetingLocation: string;

  @AutoMap()
  @ApiProperty()
  onTime: Date;

  @AutoMap()
  @ApiProperty()
  offTime: Date;

  @AutoMap()
  @ApiProperty()
  hoursUsed: Date;

  @AutoMap()
  @ApiProperty()
  engineStartHours: string;

  @AutoMap()
  @ApiProperty()
  engineOffHours: string;

  @AutoMap()
  @ApiProperty()
  dieselLevelOn: string;

  @AutoMap()
  @ApiProperty()
  dieselLevelOff: string;

  @AutoMap()
  @ApiProperty()
  lastServiceHour: Date;

  @AutoMap()
  @ApiProperty()
  nextServiceHour: Date;

  @AutoMap()
  @ApiProperty()
  dueForService: boolean;

  @AutoMap()
  @ApiProperty()
  oilFilterDueForReplacement: boolean;

  @AutoMap()
  @ApiProperty()
  LastOilFilterReplacement: Date;

  @AutoMap()
  @ApiProperty()
  faultDetected: boolean;

  @AutoMap()
  @ApiProperty()
  faultDescription: string;

  @AutoMap()
  @ApiProperty()
  personnelName: string;

  @AutoMap()
  @ApiProperty()
  Remark: string;
}
