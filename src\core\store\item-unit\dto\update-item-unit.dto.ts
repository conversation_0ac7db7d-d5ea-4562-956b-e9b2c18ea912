import { AutoMap } from '@automapper/classes';
import { Condition } from '@common/enums/condition.enum';
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional } from 'class-validator';

export class UpdateItemUnitDto {
  @AutoMap()
  @ApiProperty()
  @IsNumber()
  id: number;

  @AutoMap()
  @ApiProperty()
  @IsEnum(Condition)
  @IsOptional()
  condition: Condition;

  @AutoMap()
  @ApiProperty()
  @IsNumber()
  @IsOptional()
  storeId: number;
}
