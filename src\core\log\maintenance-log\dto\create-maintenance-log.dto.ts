import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsString } from 'class-validator';

export class CreateMaintenanceLogDto {
  @AutoMap()
  @ApiProperty()
  @IsString()
  maintenanceDate: Date;

  @AutoMap()
  @ApiProperty()
  @IsString()
  description: string;

  @AutoMap()
  @ApiProperty()
  @IsNumber()
  costOfMaintenance: number;

  @AutoMap()
  @ApiProperty()
  @IsString()
  servicedItem: string;

  @AutoMap()
  @ApiProperty()
  @IsString()
  artisanName: string;

  @AutoMap()
  @ApiProperty()
  @IsString()
  artisanPhone: string;

  @AutoMap()
  @ApiProperty()
  @IsString()
  signature: string;
}
