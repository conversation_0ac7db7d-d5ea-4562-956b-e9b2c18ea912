import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

/*
    This file was created with the following cli command:
    npm run typeorm:create-migration --name=create_user

    Find a list of all migration methods and usages at:
    https://typeorm.io/migrations
*/
export class CreateUserTable1720827482104 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    /**
     * Create User Table.
     */
    await queryRunner.createTable(
      new Table({
        name: 'user',
        columns: [
          {
            name: 'id',
            type: 'bigint',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'first_name',
            type: 'varchar',
          },
          {
            name: 'last_name',
            type: 'varchar',
          },
          {
            name: 'email',
            type: 'varchar',
            isUnique: true,
          },
          {
            name: 'phone_number',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'password',
            type: 'varchar',
            isNullable: false,
          },
          {
            name: 'gender',
            type: 'varchar',
          },
          {
            name: 'department',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'token',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'expires_at',
            type: 'bigint',
            isNullable: true,
          },
          {
            name: 'refresh_token',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'has_default_password',
            type: 'boolean',
            default: true,
          },
          {
            name: 'is_verified',
            type: 'boolean',
            default: false,
          },
          {
            name: 'status',
            type: 'varchar',
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'now()',
          },
          {
            name: 'created_by',
            type: 'varchar',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'updated_by',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'deleted_at',
            type: 'timestamp',
            isNullable: true,
          },
        ],
      }),
      true,
    );

    /**
     * Create Index
     */
    // Add an index to the user table.
    await queryRunner.createIndex(
      'user',
      new TableIndex({
        name: 'IDX_USER_EMAIL',
        columnNames: [
          'email',
          'first_name',
          'last_name',
          'gender',
          'department',
          'status',
        ],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    //Drop user table and its index
    await queryRunner.dropIndex('user', 'IDX_USER_EMAIL');
    await queryRunner.dropTable('user');
  }
}
