import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { createMap, Mapper, MappingProfile } from '@automapper/core';
import { Address } from './entities/address.entity';
import { AddressDto } from './dto/address.dto';
import { Injectable } from '@nestjs/common';
import { StoreAddressDto } from './dto/store-address.dto';
import { LoggedInUserAddressDto } from './dto/logged-in-user-address.dto';

@Injectable()
export class AddressProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }
  override get profile(): MappingProfile {
    return (mapper) => {
      createMap(mapper, Address, AddressDto);
      createMap(mapper, AddressDto, Address);
      createMap(mapper, StoreAddressDto, Address);
      createMap(mapper, Address, StoreAddressDto);
      createMap(mapper, Address, LoggedInUserAddressDto);
    };
  }
}
