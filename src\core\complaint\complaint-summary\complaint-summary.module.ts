import { Module } from '@nestjs/common';
import { ComplaintSummaryService } from './complaint-summary.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ComplaintSummary } from './entities/complaint-summary.entity';
import { AutomapperModule } from '@automapper/nestjs';

@Module({
  imports: [TypeOrmModule.forFeature([ComplaintSummary]), AutomapperModule],
  controllers: [],
  providers: [ComplaintSummaryService],
  exports: [ComplaintSummaryService],
})
export class ComplaintSummaryModule {}
