import { CurrentRoute } from '@common/decorators/current-route.decorator';
import { CurrentUser } from '@common/decorators/current-user.decorator';
import { CoreUtils } from '@common/utils/core.utils';
import { User } from '@core/security/user/entities/user.entity';
import {
  Body,
  Controller,
  DefaultValuePipe,
  Get,
  HttpStatus,
  NotFoundException,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
} from '@nestjs/common';
import {
  ApiBody,
  ApiOperation,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { Pagination } from 'nestjs-typeorm-paginate';
import { ActivateMaintenanceLogDto } from './dto/activate-maintenance-log.dto';
import { CreateMaintenanceLogDto } from './dto/create-maintenance-log.dto';
import { DeactivateMaintenanceLogDto } from './dto/deactivate-maintenance-log.dto';
import { MaintenanceLogDto } from './dto/maintenance-log.dto';
import { MaintenanceLogService } from './maintenance-log.service';
import { InjectMapper } from '@automapper/nestjs';
import { Mapper } from '@automapper/core';
import { LoggerService } from '@common/logger/logger.service';
import { MaintenanceLog } from '@core/log/maintenance-log/entities/maintenance-log.entity';

@ApiTags('Maintenance Log')
@Controller({
  path: 'maintenance-log',
  version: '1',
})
export class MaintenanceLogController {
  constructor(
    private readonly maintenanceLogService: MaintenanceLogService,
    @InjectMapper() private readonly classMapper: Mapper,
    private readonly logger: LoggerService,
  ) {
    this.logger.setContext(MaintenanceLogController.name);
  }

  @ApiOperation({ summary: 'Create new maintenance log' })
  @ApiBody({ type: CreateMaintenanceLogDto })
  @Post('new')
  async newMaintenanceLog(
    @Body() createMaintenanceLogDto: CreateMaintenanceLogDto,
    @CurrentUser() user: User,
  ) {
    return CoreUtils.handleRequest(async () => {
      const maintenanceLog: MaintenanceLog = await this.classMapper.mapAsync(
        createMaintenanceLogDto,
        CreateMaintenanceLogDto,
        MaintenanceLog,
      );
      maintenanceLog.createdBy =
        `${user.firstName} ${user.lastName}` || 'System';
      await this.maintenanceLogService.create(maintenanceLog);
      return {
        message: 'Maintenance log created successfully',
        data: null,
      };
    });
  }

  @ApiOperation({ summary: 'Update a maintenance log' })
  @ApiBody({ type: MaintenanceLogDto })
  @Patch('update/:id')
  async updateMaintenanceLog(
    @Body() updateMaintenanceDto: MaintenanceLogDto,
    @Param('id') id: number,
    @CurrentUser() user: User,
  ) {
    return CoreUtils.handleRequest(async () => {
      const maintenanceLog = await this.maintenanceLogService.findByPk(id);
      if (!maintenanceLog) {
        throw new NotFoundException('Maintenance log not found');
      }
      await this.classMapper.mutateAsync(
        updateMaintenanceDto,
        maintenanceLog,
        MaintenanceLogDto,
        MaintenanceLog,
      );
      maintenanceLog.updatedBy =
        `${user.firstName} ${user.lastName}` || 'System';
      await this.maintenanceLogService.update(maintenanceLog);
      return {
        message: 'Maintenance log updated successfully',
        data: null,
      };
    });
  }

  @ApiOperation({ summary: 'Retrieve a maintenance log' })
  @Get('detail/:id')
  async retrieveAMaintenanceLog(@Param('id') id: number) {
    return CoreUtils.handleRequest(async () => {
      const data = await this.maintenanceLogService.findByPk(id);
      return {
        message: 'Maintenance log retrieved successfully',
        data,
      };
    });
  }

  @ApiOperation({ summary: 'Activate a maintenance log' })
  @ApiBody({ type: ActivateMaintenanceLogDto })
  @Patch('activate')
  async activate(@Body() body: ActivateMaintenanceLogDto) {
    return CoreUtils.handleRequest(async () => {
      await this.maintenanceLogService.activate(body.ids);
      return {
        message: `Maintenance log${
          body.ids.length > 1 ? 's' : ''
        } activated successfully`,
        data: null,
      };
    });
  }

  @ApiOperation({ summary: 'Deactivate a maintenance log' })
  @ApiBody({ type: DeactivateMaintenanceLogDto })
  @Patch('deactivate')
  async deactivate(@Body() body: DeactivateMaintenanceLogDto) {
    return CoreUtils.handleRequest(async () => {
      await this.maintenanceLogService.deactivate(body.ids);
      return {
        message: `Maintenance log${
          body.ids.length > 1 ? 's' : ''
        } deactivated successfully`,
        data: null,
      };
    });
  }

  @Get()
  @ApiOperation({ summary: 'Get paginated list of Maintenance logs' })
  @ApiQuery({ name: 'page', required: true, type: Number, example: 1 })
  @ApiQuery({ name: 'limit', required: true, type: Number, example: 10 })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'filter', required: false, type: String })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Successful response with paginated Maintenance logs',
    type: Pagination,
  })
  async getAllMaintenanceLogs(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit,
    @Query('search') search: string,
    @Query('filter') filter: string,
    @CurrentRoute() route: string,
  ) {
    return CoreUtils.handleRequest(async () => {
      const data = await this.maintenanceLogService.getPaginatedMaintenanceLogs(
        {
          page,
          limit,
          search,
          filter,
        },
        route,
      );
      return { message: 'Maintenance logs retrieved successfully', data };
    });
  }

  // server-side search

  // 1) Without pagination
  @ApiOperation({ summary: 'Search for maintenance logs' })
  @Get('/search')
  async searchMaintenanceLog(@Query('q') query: string) {
    return CoreUtils.handleRequest(async () => {
      const data = await this.maintenanceLogService.search(query);
      return {
        message: 'Maintenance logs retrieved successfully',
        data,
      };
    });
  }

  // 2) With pagination
  @ApiOperation({ summary: 'Paginated search for maintenance logs' })
  @Get('/paginate-search')
  async paginateMaintenanceLogSearch(
    @Query('q') query: string,
    @Query('page') page: number,
    @Query('limit') limit: number,
  ) {
    return CoreUtils.handleRequest(async () => {
      const data = await this.maintenanceLogService.paginatedSearch(
        query,
        page,
        limit,
      );
      return {
        message: 'Maintenance logs retrieved successfully',
        data,
      };
    });
  }
}
