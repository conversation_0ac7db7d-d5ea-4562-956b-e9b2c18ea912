import { Test, TestingModule } from '@nestjs/testing';
import { GeneratorLogController } from './generator-log.controller';
import { GeneratorLogService } from './generator-log.service';

describe('GeneratorLogController', () => {
  let controller: GeneratorLogController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [GeneratorLogController],
      providers: [GeneratorLogService],
    }).compile();

    controller = module.get<GeneratorLogController>(GeneratorLogController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
