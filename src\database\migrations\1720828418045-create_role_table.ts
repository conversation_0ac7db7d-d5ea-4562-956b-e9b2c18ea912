import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateRoleTable1720828418045 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Role Table
    await queryRunner.createTable(
      new Table({
        name: 'role',
        columns: [
          {
            name: 'id',
            type: 'bigint',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'name',
            type: 'varchar',
            isUnique: true,
          },
          {
            name: 'description',
            type: 'varchar',
          },
          {
            name: 'status',
            type: 'varchar',
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'now()',
          },
          {
            name: 'created_by',
            type: 'varchar',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'updated_by',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'deleted_at',
            type: 'timestamp',
            isNullable: true,
          },
        ],
      }),
      true,
    );

    // Add an index to the role table.
    await queryRunner.createIndex(
      'role',
      new TableIndex({
        name: 'IDX_ROLE_NAME',
        columnNames: ['name'],
      }),
    );

    // Create join table for USER and ROLE
    await queryRunner.createTable(
      new Table({
        name: 'user_role',
        columns: [
          {
            name: 'user_id',
            type: 'int',
          },
          {
            name: 'role_id',
            type: 'int',
          },
        ],
      }),
      true,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    //Drop the join table
    await queryRunner.dropTable('user_role');
    //Drop role table and its index
    await queryRunner.dropIndex('role', 'IDX_ROLE_NAME');
    await queryRunner.dropTable('role');
  }
}
