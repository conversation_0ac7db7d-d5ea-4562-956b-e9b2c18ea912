import { CoreConstants } from '@common/utils/core.constants';
import { RoleDto } from '@core/security/role/dto/role.dto';
import { UserDto } from '@core/security/user/dto/user.dto';
import { Gender } from '@common/enums/gender.enum';
import { Department } from '@common/enums/department.enum';
import { Store } from '@common/enums/store.enum';
import { CreateStoreDto } from '@core/store/dto/create-store.dto';
import { CoreUtils } from '@common/utils/core.utils';
import { DepartmentDto } from '@core/security/department/dto/department.dto';
import { MemberType } from '@common/enums/member-type.enum';

export const roles: Array<RoleDto> = [
  { name: CoreConstants.DEFAULT_ADMIN_ROLE, description: 'Admin' },
  { name: CoreConstants.DEFAULT_ROLE, description: 'Default User Role' },
  { name: CoreConstants.HOD_ROLE, description: 'Department Head Role' },
  { name: CoreConstants.MEMBER_ROLE, description: 'Department Member Role' },
  { name: CoreConstants.SUPER_ADMIN_ROLE, description: 'Super Admin Role' },
];

export const departments: Array<DepartmentDto> = [
  {
    hodEmail: '<EMAIL>',
    hodName: 'Ime Bassey',
    hodPhone: '+2348032753305',
    name: Department.FACILITY,
  },
  {
    hodEmail: '<EMAIL>',
    hodName: 'Arogundade Opeyemi',
    hodPhone: '+2348055641081',
    name: Department.LOGISTICS,
  },
  {
    hodEmail: null,
    hodName: null,
    hodPhone: null,
    name: Department.WORSHIP_TEAM,
  },
  {
    hodName: null,
    hodPhone: null,
    hodEmail: null,
    name: Department.CHILDCARE,
  },
  {
    hodEmail: null,
    hodName: null,
    hodPhone: null,
    name: Department.KITCHEN_SUPPORT,
  },
  {
    hodEmail: null,
    hodName: null,
    hodPhone: null,
    name: Department.MEDICAL_TEAM,
  },
  {
    hodName: null,
    hodPhone: null,
    hodEmail: null,
    name: Department.ADMIN_SECRETARY,
  },
  {
    hodEmail: null,
    hodName: null,
    hodPhone: null,
    name: Department.LIGHTING,
  },
  {
    hodName: null,
    hodPhone: null,
    hodEmail: null,
    name: Department.USHERING,
  },
  {
    hodName: null,
    hodPhone: null,
    hodEmail: null,
    name: Department.SANITATION,
  },
  {
    hodEmail: null,
    hodName: null,
    hodPhone: null,
    name: Department.SOUND,
  },
  {
    hodEmail: null,
    hodName: null,
    hodPhone: null,
    name: Department.VISUAL_MEDIA,
  },
  {
    hodEmail: null,
    hodName: null,
    hodPhone: null,
    name: Department.PROTOCOL,
  },
  {
    hodEmail: null,
    hodName: null,
    hodPhone: null,
    name: Department.ELECTRICAL,
  },
  {
    hodEmail: null,
    hodName: null,
    hodPhone: null,
    name: Department.VIDEO,
  },
  {
    hodEmail: null,
    hodName: null,
    hodPhone: null,
    name: Department.COMMUNICATION,
  },
  {
    hodEmail: null,
    hodName: null,
    hodPhone: null,
    name: Department.E_CHURCH,
  },
  {
    hodEmail: null,
    hodName: null,
    hodPhone: null,
    name: Department.CONFERENCE_MANAGEMENT,
  },
  {
    hodEmail: null,
    hodName: null,
    hodPhone: null,
    name: Department.ERU,
  },
  {
    hodEmail: null,
    hodName: null,
    hodPhone: null,
    name: Department.SECURITY,
  },
  {
    hodEmail: null,
    hodName: null,
    hodPhone: null,
    name: Department.FOOD_SERVICE,
  },
  {
    hodEmail: null,
    hodName: null,
    hodPhone: null,
    name: Department.KITCHEN,
  },
  {
    hodEmail: null,
    hodName: null,
    hodPhone: null,
    name: Department.PROJECTION,
  },
  {
    hodEmail: null,
    hodName: null,
    hodPhone: null,
    name: Department.DECORATION,
  },
  {
    hodEmail: null,
    hodName: null,
    hodPhone: null,
    name: Department.PASTORAL_CARE,
  },
  {
    hodEmail: null,
    hodName: null,
    hodPhone: null,
    name: Department.EDITORIAL,
  },
  {
    hodEmail: null,
    hodName: null,
    hodPhone: null,
    name: Department.ADMINISTRATION,
  },
  {
    hodEmail: null,
    hodName: null,
    hodPhone: null,
    name: Department.LIBRARY,
  },
  {
    hodEmail: null,
    hodName: null,
    hodPhone: null,
    name: Department.TRANSPORT,
  },
  {
    hodEmail: null,
    hodName: null,
    hodPhone: null,
    name: Department.CAMP_COMMANDANT,
  },
  {
    hodEmail: null,
    hodName: null,
    hodPhone: null,
    name: Department.CAMP_COMMANDANT,
  },
  {
    hodEmail: null,
    hodName: null,
    hodPhone: null,
    name: Department.MINISTERS_KITCHEN,
  },
];

export const stores: Array<CreateStoreDto> = [
  { name: Store.NOIC, location: null },
  { name: Store.KOSOFE, location: null },
  { name: Store.AAEC, location: null },
];

export const users: Array<UserDto> = [
  {
    email: '<EMAIL>',
    lastName: 'Bassey',
    firstName: 'Ime',
    password: CoreUtils.hash('Syst3m5P@s5W0rd'),
    department: Department.FACILITY,
    phoneNumber: '2348032753305',
    gender: Gender.MALE,
    role: CoreConstants.SUPER_ADMIN_ROLE,
  },
  {
    email: '<EMAIL>',
    lastName: 'Arogundade',
    firstName: 'Opeyemi',
    password: CoreUtils.hash('Syst3m5P@s5W0rd'),
    department: Department.LOGISTICS,
    phoneNumber: '2348055641081',
    gender: Gender.MALE,
    role: CoreConstants.DEFAULT_ADMIN_ROLE,
  },
];
