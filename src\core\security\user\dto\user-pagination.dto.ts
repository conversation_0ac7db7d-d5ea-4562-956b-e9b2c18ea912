import { AutoMap } from '@automapper/classes';
import { EntityDto } from '@common/dto/base.dto';
import { Gender } from '@common/enums/gender.enum';
import { ApiProperty } from '@nestjs/swagger';

export class UserPaginationDto extends EntityDto {
  @AutoMap()
  @ApiProperty()
  email: string;

  @AutoMap()
  @ApiProperty()
  firstName?: string;

  @AutoMap()
  @ApiProperty()
  lastName?: string;

  @AutoMap()
  @ApiProperty()
  phoneNumber?: string;

  @AutoMap()
  @ApiProperty({
    type: 'string',
    enum: Object.values(Gender),
    name: 'gender',
    description: 'Possible genders',
    default: Gender.DEFAULT,
  })
  gender: Gender;

  @AutoMap()
  @ApiProperty()
  role: string;

  @AutoMap()
  @ApiProperty()
  isVerified: boolean;
}
