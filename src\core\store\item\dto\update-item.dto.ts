import { AutoMap } from '@automapper/classes';
import { UpdateItemUnitDto } from '@core/store/item-unit/dto/update-item-unit.dto';
import { PartialType } from '@nestjs/mapped-types';
import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsOptional, ValidateNested } from 'class-validator';
import { CreateItemDto } from './create-item.dto';
import { Type } from 'class-transformer';

export class UpdateItemDto extends PartialType(CreateItemDto) {
  @AutoMap(() => UpdateItemUnitDto)
  @ApiProperty()
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => UpdateItemUnitDto)
  itemUnits: UpdateItemUnitDto[];
}
