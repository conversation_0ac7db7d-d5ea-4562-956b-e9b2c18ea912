import { Injectable } from '@nestjs/common';
import {
  AbilityBuilder,
  AbilityClass,
  ExtractSubjectType,
  InferSubjects,
  PureAbility,
} from '@casl/ability';
import { LoggerService } from '@common/logger/logger.service';
import { User } from '../../user/entities/user.entity';
import { UserActions } from '@common/enums/user-actions.enum';
import { Role } from '../../role/entities/role.entity';
import { Department } from '@core/security/department/entities/department.entity';

export type Subjects =
  | InferSubjects<typeof User | typeof Role | typeof Department>
  | 'all';

export type AppAbility = PureAbility<[UserActions, Subjects]>;

@Injectable()
export class CaslAbilityFactoryService {
  constructor(private readonly logger: LoggerService) {
    this.logger.setContext(CaslAbilityFactoryService.name);
  }

  createForUser(user: User) {
    const { can, cannot, build } = new AbilityBuilder<AppAbility>(
      PureAbility as AbilityClass<AppAbility>,
    );

    user.role.permissions.forEach((permission) => {
      if (permission.name === UserActions.MANAGE) {
        can(UserActions.MANAGE, 'all'); // Can manage all subjects
      } else {
        can(permission.name as UserActions, User); // Adjust this for specific permissions
      }
    });

    // Example: Prevent 'delete' on 'User'
    cannot(UserActions.DELETE, User).because("You can't delete users.");
    cannot(UserActions.DELETE, Role).because("You can't delete roles.");

    return build({
      detectSubjectType: (item) =>
        item.constructor as ExtractSubjectType<Subjects>,
    });
  }
}
