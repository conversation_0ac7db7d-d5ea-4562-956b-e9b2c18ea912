import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { UserService } from '../../user/user.service';
import { ConfigService } from '@nestjs/config';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { User } from '../../user/entities/user.entity';
import { JwtPayload } from '../types/jwt-payload.type';
import { Role } from '../../role/entities/role.entity';

export type AccessTokenPayload = {
  id: number;
  email: string;
  firstName: string;
  lastName: string;
  role: Array<Role>;
  verified: boolean;
  refreshToken?: string;
};

@Injectable()
export class AccessTokenStrategy extends PassportStrategy(Strategy, 'jwt') {
  constructor(
    private userService: UserService,
    private configService: ConfigService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      secretOrKey: configService.get<string>('keys.secret'),
      // algorithms: ['RS256'],
    });
  }

  /**
   * @description Validate the token and return the user
   * @param request
   * @param payload string
   * @returns User
   */
  async validate(payload: JwtPayload) {
    // Accept the JWT and attempt to validate it using the user service
    const existingUser: User = await this.userService.findByEmail(
      payload.email,
    );

    // If the user is not found, throw an error
    if (!existingUser) {
      throw new UnauthorizedException(
        `User with email ${payload.email} not found`,
      );
    }
    return existingUser;
  }
}
