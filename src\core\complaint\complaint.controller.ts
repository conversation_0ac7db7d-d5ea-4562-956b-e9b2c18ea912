import { CurrentRoute } from '@common/decorators/current-route.decorator';
import { Public } from '@common/decorators/public.decorator';
import { CoreUtils } from '@common/utils/core.utils';
import {
  Body,
  Controller,
  DefaultValuePipe,
  Get,
  HttpStatus,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
} from '@nestjs/common';
import {
  ApiBody,
  ApiOperation,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { Pagination } from 'nestjs-typeorm-paginate';
import { ComplaintService } from './complaint.service';
import { ActivateComplaintDto } from './dto/activate-complaint.dto';
import { CreateComplaintDto } from './dto/create-complaint.dto';
import { DeactivateComplaintDto } from './dto/deactivate-complaint.dto';

@ApiTags('Complaint')
@Controller({
  path: 'complaint',
  version: '1',
})
export class ComplaintController {
  constructor(private readonly complaintService: ComplaintService) {}

  @ApiOperation({ summary: 'Activate complaints' })
  @ApiBody({ type: ActivateComplaintDto })
  @Patch('activate')
  async activate(@Body() body: ActivateComplaintDto) {
    return CoreUtils.handleRequest(async () => {
      await this.complaintService.activate(body.ids);
      return {
        message: `Complaint${
          body.ids.length > 1 ? 's' : ''
        } activated successfully`,
        data: null,
      };
    });
  }

  @ApiOperation({ summary: 'Deactivate complaints' })
  @ApiBody({ type: DeactivateComplaintDto })
  @Patch('deactivate')
  async deactivate(@Body() body: DeactivateComplaintDto) {
    return CoreUtils.handleRequest(async () => {
      await this.complaintService.deactivate(body.ids);
      return {
        message: `Complaint${
          body.ids.length > 1 ? 's' : ''
        } deactivated successfully`,
        data: null,
      };
    });
  }

  @Get()
  @ApiOperation({ summary: 'Get paginated list of complaints' })
  @ApiQuery({ name: 'page', required: true, type: Number, example: 1 })
  @ApiQuery({ name: 'limit', required: true, type: Number, example: 10 })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'filter', required: false, type: String })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Successful response with paginated requests',
    type: Pagination,
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Internal server error',
  })
  async getAllComplaints(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit,
    @Query('search') search: string,
    @Query('filter') filter: string,
    @CurrentRoute() route: string,
  ) {
    return CoreUtils.handleRequest(async () => {
      const data = await this.complaintService.getPaginatedComplaints(
        {
          page,
          limit,
          search,
          filter,
        },
        route,
      );
      return {
        message: 'Complaints retrieved successfully',
        data,
      };
    });
  }

  @Public()
  @ApiOperation({ summary: 'Create a new complaint' })
  @ApiBody({ type: CreateComplaintDto })
  @Post('new')
  async create(@Body() createComplaintDto: CreateComplaintDto) {
    return CoreUtils.handleRequest(async () => {
      await this.complaintService.create(createComplaintDto);
      return {
        message: 'Complaint created successfully',
        data: null,
      };
    });
  }

  @ApiOperation({ summary: 'Retrieve a complaint' })
  @Get('detail/:complaintId')
  async retrieveAComplaint(@Param('complaintId') complaintId: number) {
    return CoreUtils.handleRequest(async () => {
      const data = await this.complaintService.getComplaint(complaintId);
      return {
        message: 'Complaint retrieved successfully',
        data,
      };
    });
  }

  @ApiOperation({ summary: 'Search for complaints' })
  @ApiQuery({ name: 'q', required: true, type: String, example: 'john' })
  @Get('/search')
  async searchComplaints(@Query('q') query: string) {
    return CoreUtils.handleRequest(async () => {
      const data = await this.complaintService.search(query);
      return {
        message: 'Complaints retrieved successfully',
        data,
      };
    });
  }
}
