import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateRequestTable1752617260389 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    /**
     * Create Request Table.
     */
    await queryRunner.createTable(
      new Table({
        name: 'request',
        columns: [
          {
            name: 'id',
            type: 'bigint',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'requester_name',
            type: 'varchar',
          },
          {
            name: 'requester_email',
            type: 'varchar',
          },
          {
            name: 'requester_phone',
            type: 'varchar',
          },
          {
            name: 'is_ministry',
            type: 'boolean',
            default: false,
          },
          {
            name: 'ministry_name',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'is_church',
            type: 'boolean',
            default: false,
          },
          {
            name: 'church_name',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'requester_department_id',
            type: 'int',
          },
          {
            name: 'requester_hod_name',
            type: 'varchar',
          },
          {
            name: 'requester_hod_email',
            type: 'varchar',
          },
          {
            name: 'requester_hod_phone',
            type: 'varchar',
          },
          {
            name: 'location_of_use',
            type: 'varchar',
          },
          {
            name: 'duration_of_use',
            type: 'timestamp',
          },
          {
            name: 'date_of_return',
            type: 'timestamp',
          },
          {
            name: 'description_of_request',
            type: 'text',
          },
          {
            name: 'request_status',
            type: 'varchar',
          },
          {
            name: 'status',
            type: 'varchar',
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'now()',
          },
          {
            name: 'created_by',
            type: 'varchar',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'updated_by',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'deleted_at',
            type: 'timestamp',
            isNullable: true,
          },
        ],
      }),
      true,
    );

    /**
     * Create Index
     */
    // Add an index to the user table.
    await queryRunner.createIndex(
      'request',
      new TableIndex({
        name: 'IDX_REQUESTER_NAME',
        columnNames: ['requester_name'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    //Drop user table and its index
    await queryRunner.dropIndex('request', 'IDX_REQUESTER_NAME');
    await queryRunner.dropTable('request');
  }
}
