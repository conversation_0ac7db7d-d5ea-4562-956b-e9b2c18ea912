import { Injectable } from '@nestjs/common';
import { IPaginationOptions, Pagination } from 'nestjs-typeorm-paginate';
import { LoggerService } from '@common/logger/logger.service';
import { Complaint } from '@core/complaint/entities/complaint.entity';

@Injectable()
export class ComplaintSummaryService {
  constructor(private readonly logger: LoggerService) {
    this.logger.setContext(ComplaintSummaryService.name);
  }

  activate(ids: Array<number>): Promise<void> {
    return Promise.resolve(undefined);
  }

  create(entity: Complaint): Promise<Complaint> {
    return Promise.resolve(undefined);
  }

  deactivate(ids: Array<number>): Promise<void> {
    return Promise.resolve(undefined);
  }

  delete(id: number): Promise<void> {
    return Promise.resolve(undefined);
  }

  findAll(): Promise<Array<Complaint>> {
    return Promise.resolve(undefined);
  }

  findByPk(id: number): Promise<Complaint> {
    return Promise.resolve(undefined);
  }

  paginate(
    options: IPaginationOptions,
    where?: any,
    ...args: any[]
  ): Promise<Pagination<Complaint>> {
    return Promise.resolve(undefined);
  }

  paginatedSearch(
    query: string,
    page: number,
    limit: number,
  ): Promise<Array<Complaint>> {
    return Promise.resolve(undefined);
  }

  search(query: string): Promise<Array<Complaint>> {
    return Promise.resolve(undefined);
  }

  update(entity: Complaint): Promise<Complaint> {
    return Promise.resolve(undefined);
  }
}
