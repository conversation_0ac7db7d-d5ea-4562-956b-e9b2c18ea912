import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateItemTable1752657448902 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    //Create Item table
    await queryRunner.createTable(
      new Table({
        name: 'item',
        columns: [
          {
            name: 'id',
            type: 'bigint',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'name',
            type: 'varchar',
          },
          {
            name: 'available_quantity',
            type: 'bigint',
          },
          {
            name: 'actual_quantity',
            type: 'bigint',
          },
          {
            name: 'fragile',
            type: 'boolean',
          },
          {
            name: 'picture_url',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'status',
            type: 'varchar',
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'now()',
          },
          {
            name: 'created_by',
            type: 'varchar',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'updated_by',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'deleted_at',
            type: 'timestamp',
            isNullable: true,
          },
        ],
      }),
      true,
    );

    // Add an index to the item table.
    await queryRunner.createIndex(
      'item',
      new TableIndex({
        name: 'IDX_ITEM_NAME',
        columnNames: ['name', 'fragile', 'status'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    //Drop table and its index
    await queryRunner.dropIndex('item', 'IDX_ITEM_NAME');
    await queryRunner.dropTable('item');
  }
}
