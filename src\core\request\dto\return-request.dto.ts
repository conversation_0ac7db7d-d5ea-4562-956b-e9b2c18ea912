import { AutoMap } from '@automapper/classes';
import { CreateItemAuditDto } from '@core/store/item-audit/dto/create-item-audit.dto';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, ValidateNested } from 'class-validator';

export class ReturnRequestDto {
  @AutoMap(() => CreateItemAuditDto)
  @ApiProperty({ type: CreateItemAuditDto, isArray: true })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateItemAuditDto)
  items: Array<CreateItemAuditDto>;
}
