import { config } from 'dotenv';
import { Logger } from '@nestjs/common';
import migrations from '../database/migrations/index';
import { PostgresConnectionOptions } from 'typeorm/driver/postgres/PostgresConnectionOptions';

config({ path: '.env' });

export const dbConfig = (): PostgresConnectionOptions => {
  const isCloudHost = process.env.PG_HOST && (
    process.env.PG_HOST.includes('aiven') ||
    process.env.PG_HOST.includes('heroku') ||
    process.env.PG_HOST.includes('aws') ||
    process.env.PG_HOST.includes('azure') ||
    process.env.PG_HOST.includes('cloud')
  );
  const useSSL = isCloudHost || process.env.NODE_ENV !== 'development';

  return {
    type: 'postgres',
    host: process.env.PG_HOST,
    port: parseInt(process.env.PG_PORT, 10) || 5432,
    // url: process.env.HEROKU_POSTGRESQL_GREEN_URL
    //   ? process.env.HEROKU_POSTGRESQL_GREEN_URL
    //   : process.env.DATABASE_URL,
    username: process.env.PG_USERNAME,
    password: process.env.PG_PASSWORD,
    database: process.env.PG_DATABASE,
    entities: [__dirname + '/../**/*.entity{.ts,.js}'],
    ssl: useSSL ? { rejectUnauthorized: false } : false, // Enable SSL for cloud DBs or non-development
    // We are using migrations, synchronize should be set to false.
    synchronize: false,
    dropSchema: false,

    // Run migrations automatically,
    // you can prefer running migration manually.
    migrationsRun: false,
    logging: true,
    migrations: migrations,
  };
};

// Export the database configuration for TypeORM
export const databaseConfig = dbConfig();

if (process.env.NODE_ENV === 'development') {
  Logger.debug(dbConfig());
}
