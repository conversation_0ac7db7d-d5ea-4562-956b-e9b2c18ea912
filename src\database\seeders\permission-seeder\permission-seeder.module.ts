import { Module } from '@nestjs/common';
import { Permission } from '../../../core/security/permission/entities/permission.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PermissionTableSeeder } from './permission.seeder';
import { LoggerModule } from '../../../common/logger/logger.module';

@Module({
  exports: [PermissionTableSeeder],
  imports: [TypeOrmModule.forFeature([Permission]), LoggerModule],
  providers: [PermissionTableSeeder],
})
export class PermissionSeederModule {}
