import { Permission } from '../../../core/security/permission/entities/permission.entity';
import { Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { permissions } from './data';
import { InjectRepository } from '@nestjs/typeorm';
import { LoggerService } from '../../../common/logger/logger.service';

@Injectable()
export class PermissionTableSeeder {
  constructor(
    @InjectRepository(Permission)
    private readonly permissionRepository: Repository<Permission>,
    private readonly logger: LoggerService,
  ) {
    this.logger.setContext(PermissionTableSeeder.name);
  }

  async seed(): Promise<number> {
    let createdCount = 0;
    for (const permission of permissions) {
      try {
        const existingPermission = await this.permissionRepository.findOne({
          where: { name: permission.name },
        });

        if (!existingPermission) {
          const newPermission = this.permissionRepository.create({
            name: permission.name,
            description: permission.description,
          });
          await this.permissionRepository.save(newPermission);
          createdCount++;
        }
      } catch (error) {
        this.logger.error(
          `Error creating permission ${permission.name}: ${error}`,
        );
      }
    }
    return createdCount;
  }
}
