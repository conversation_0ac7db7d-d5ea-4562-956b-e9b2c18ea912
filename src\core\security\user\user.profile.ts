import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import {
  createMap,
  Mapper,
  MappingProfile,
  forMember,
  mapFrom,
} from '@automapper/core';
import { User } from './entities/user.entity';
import { UserDto } from './dto/user.dto';
import { UserProfileDto } from './dto/user-profile.dto';
import { Injectable } from '@nestjs/common';
import { OnboardUserDto } from '../authentication/dto/onboard-user.dto';
import { InitiateUserDto } from './dto/initiate-user.dto';
import { LoggedInUserDto } from './dto/logged-in-user.dto';
import { UserDetailDto } from './dto/user-detail.dto';
import { UserPaginationDto } from './dto/user-pagination.dto';
import { CreateUserDto } from './dto/create-user.dto';

@Injectable()
export class UserProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile(): MappingProfile {
    return (mapper) => {
      // createMap(mapper, User, UserDto);
      createMap(
        mapper,
        User,
        UserDto,
        forMember(
          (destination) => destination.role,
          mapFrom((source) => source.role?.name),
        ),
        forMember(
          (destination) => destination.department,
          mapFrom((source) => source.department?.name || ''),
        ),
      );
      createMap(mapper, UserDto, User);
      createMap(mapper, CreateUserDto, User);
      createMap(mapper, User, UserProfileDto);
      createMap(mapper, OnboardUserDto, User);
      createMap(mapper, InitiateUserDto, User);
      createMap(mapper, User, LoggedInUserDto);
      createMap(mapper, User, UserDetailDto);
      createMap(
        mapper,
        User,
        UserPaginationDto,
        forMember(
          (destination) => destination.role,
          mapFrom((source) => source.role?.name),
        ),
      );
    };
  }
}
