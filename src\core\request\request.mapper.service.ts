import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import {
  createMap,
  forMember,
  mapFrom,
  Mapper,
  MappingProfile,
} from '@automapper/core';
import { RequestDto } from '@core/request/dto/request.dto';
import { Request } from '@core/request/entities/request.entity';
import { CreateRequestDto } from '@core/request/dto/create-request.dto';
import { RequestTableDto } from '@core/request/dto/request-table.dto';
import { RequestDetailDto } from '@core/request/dto/request-detail.dto';
import { RequestAudit } from '@core/request/request-audit/entities/request-audit.entity';
import { RequestAuditDto } from '@core/request/request-audit/dto/request-audit.dto';
import { ItemAudit } from '@core/store/item-audit/entities/item-audit.entity';
import { CreateItemAuditDto } from '@core/store/item-audit/dto/create-item-audit.dto';

@Injectable()
export class RequestMapper extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile(): MappingProfile {
    return (mapper) => {
      createMap(mapper, RequestDto, Request);
      createMap(
        mapper,
        Request,
        RequestDto,
        forMember(
          (destination) => destination.audit,
          mapFrom((source) =>
            this.mapper.map(source.audit, RequestAudit, RequestAuditDto),
          ),
        ),
      );
      createMap(
        mapper,
        CreateRequestDto,
        Request,
        forMember(
          (destination) => destination.requesterDepartmentId,
          mapFrom((source) => Number(source.requesterDepartmentId)),
        ),
        forMember(
          (destination) => destination.dateOfReturn,
          mapFrom((source) =>
            source.dateOfReturn ? new Date(source.dateOfReturn) : null,
          ),
        ),
        forMember(
          (destination) => destination.audit.items,
          mapFrom((source) =>
            this.mapper.mapArray(source.items, CreateItemAuditDto, ItemAudit),
          ),
        ),
      );
      createMap(mapper, RequestTableDto, Request);
      createMap(mapper, Request, RequestTableDto);
      createMap(mapper, Request, RequestDetailDto);
    };
  }
}
