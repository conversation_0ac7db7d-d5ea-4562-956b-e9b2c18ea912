import { Condition } from '@common/enums/condition.enum';
import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';

export class ItemUnitAuditDto {
  @AutoMap()
  @ApiProperty({
    type: Number,
    description: 'Unique identifier for the store',
    example: 1,
    name: 'storeId',
  })
  storeId: number;

  @AutoMap()
  @ApiProperty({
    type: String,
    description: 'Name of the store',
    example: 'Main Store',
    name: 'storeName',
  })
  storeName: string;

  @AutoMap()
  @ApiProperty({
    type: String,
    description: 'Serial number of the item unit',
    example: 'EGFM/USH/OFF/009',
    name: 'serialNumber',
  })
  serialNumber: string;

  @AutoMap(() => String)
  @ApiProperty({
    type: 'string',
    enum: Object.values(Condition),
    name: 'condition',
    description: 'Condition of the item unit',
    default: Condition.NOT_SPECIFIED,
  })
  condition: Condition;
}
