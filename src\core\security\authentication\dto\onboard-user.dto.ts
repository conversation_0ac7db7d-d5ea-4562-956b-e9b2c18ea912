import { AutoMap } from '@automapper/classes';
import { IsEmail, IsEnum, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Gender } from '@common/enums/gender.enum';

export class OnboardUserDto {
  @AutoMap()
  @ApiProperty()
  @IsEmail()
  email: string;

  @AutoMap()
  @ApiProperty()
  @IsString()
  firstName?: string;

  @AutoMap()
  @ApiProperty()
  @IsString()
  lastName?: string;

  @AutoMap()
  @ApiProperty()
  @IsString()
  phoneNumber?: string;

  @AutoMap()
  @ApiProperty()
  @IsString()
  password: string;

  @AutoMap()
  @ApiProperty()
  @IsString()
  department: string;

  @AutoMap(() => String)
  @ApiProperty({
    type: 'string',
    enum: Object.values(Gender),
    name: 'userType',
    description: 'Gender of user',
  })
  @IsEnum(Gender)
  gender: Gender;
}
