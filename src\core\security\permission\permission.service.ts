import { Injectable } from '@nestjs/common';
import { Permission } from './entities/permission.entity';
import {
  IPaginationOptions,
  paginate,
  paginateRaw,
  Pagination,
} from 'nestjs-typeorm-paginate';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityStatus } from '../../../common/entities/base.entity';
import { LoggerService } from '../../../common/logger/logger.service';

@Injectable()
export class PermissionService {
  constructor(
    @InjectRepository(Permission)
    private permissionRepository: Repository<Permission>,
    private readonly logger: LoggerService,
  ) {
    this.logger.setContext(PermissionService.name);
  }

  async create(permission: Permission): Promise<Permission> {
    // run create validation here
    return this.permissionRepository.save(permission);
  }

  async findAll(): Promise<Permission[]> {
    return this.permissionRepository.find();
  }

  async findAllByPk(permissions: number[]): Promise<Permission[]> {
    return await this.permissionRepository
      .createQueryBuilder('permission')
      .where('permission.id IN (:...ids)', { ids: permissions })
      .getMany();
  }

  async findAllAvailablePermissions(roleId: number): Promise<Permission[]> {
    return this.permissionRepository
      .createQueryBuilder('permission')
      .where(
        `permission.id not in (select permission_id from role_permission where role_id = ${roleId})`,
      )
      .getMany();
  }

  async findAllAssignedPermissions(roleId: number): Promise<Permission[]> {
    return this.permissionRepository
      .createQueryBuilder('permission')
      .where(
        `permission.id in (select permission_id from role_permission where role_id = ${roleId})`,
      )
      .getMany();
  }

  async findByPk(id: number): Promise<Permission> {
    return this.permissionRepository.findOneBy({ id });
  }

  async findByName(name: string): Promise<Permission> {
    return this.permissionRepository.findOneBy({ name });
  }

  async update(permission: Permission): Promise<Permission> {
    // run update validation here
    return this.permissionRepository.save(permission);
  }

  async remove(id: number): Promise<void> {
    await this.permissionRepository.delete(id);
  }

  async paginateRaw(
    queryBuilder: SelectQueryBuilder<Permission>,
    options: IPaginationOptions,
    where?: any,
  ): Promise<Pagination<Permission>> {
    if (where && Object.keys(where).length > 0) {
      return paginateRaw(queryBuilder, options);
    }
    return paginateRaw(queryBuilder, options);
  }

  async paginate(
    options: IPaginationOptions,
    where?: any,
  ): Promise<Pagination<Permission>> {
    if (where && Object.keys(where).length > 0) {
      return paginate<Permission>(this.permissionRepository, options, {
        where,
        order: {
          createdAt: 'DESC',
        },
      });
    }
    return paginate<Permission>(this.permissionRepository, options);
  }

  async assignedPermissions(
    roleId: number,
    page: number,
    limit: number,
    search: string,
  ): Promise<Pagination<Permission>> {
    let queryBuilder = this.permissionRepository
      .createQueryBuilder('permission')
      .where(
        `permission.id in (select permission_id from role_permission where role_id = ${roleId})`,
      );
    if (search) {
      queryBuilder = queryBuilder.andWhere(
        '(permission.name like :search or permission.description like :search)',
        { search: `%${search}%` },
      );
    }
    return await this.paginateRaw(queryBuilder, {
      page,
      limit,
      route: `/api/permission/assigned`,
    });
  }

  async availablePermissions(
    roleId: number,
    page: number,
    limit: number,
    search: string,
  ): Promise<Pagination<Permission>> {
    let queryBuilder = this.permissionRepository
      .createQueryBuilder('permission')
      .where(
        `permission.id not in (select permission_id from role_permission where role_id = ${roleId})`,
      );

    if (search) {
      queryBuilder = queryBuilder.andWhere(
        '(permission.name like :search or permission.description like :search)',
        { search: `%${search}%` },
      );
    }
    return await this.paginateRaw(queryBuilder, {
      page,
      limit,
      route: `/api/permission/available`,
    });
  }

  async activate(ids: Array<number>): Promise<void> {
    ids.map(async (id) => {
      const permission: Permission = await this.findByPk(id);
      permission.status = EntityStatus.ACTIVE;
      await this.update(permission);
    });
  }

  async deactivate(ids: Array<number>): Promise<void> {
    ids.map(async (id) => {
      const permission: Permission = await this.findByPk(id);
      permission.status = EntityStatus.INACTIVE;
      await this.update(permission);
    });
  }

  delete(id: number): Promise<void> {
    return Promise.resolve(undefined);
  }

  paginatedSearch(
    query: string,
    page: number,
    limit: number,
  ): Promise<Array<Permission>> {
    return Promise.resolve(undefined);
  }

  search(query: string): Promise<Array<Permission>> {
    return Promise.resolve(undefined);
  }

  async findByIds(ids: Array<number>): Promise<Permission[]> {
    const list: Array<Permission> = [];
    for (const id of ids) {
      this.logger.debug(`Finding permission with id: ${id}`);
      list.push(await this.permissionRepository.findOne({ where: { id } }));
    }
    return list;
  }
}
