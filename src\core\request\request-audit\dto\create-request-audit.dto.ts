import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { CreateItemAuditDto } from '@core/store/item-audit/dto/create-item-audit.dto';
import { IsArray, IsBoolean, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateRequestAuditDto {
  @AutoMap()
  @ApiProperty({
    type: Boolean,
    default: false,
    description: 'Indicates whether the request has been assigned',
    example: true,
    name: 'assigned',
  })
  @IsBoolean()
  assigned: boolean;

  @AutoMap()
  @ApiProperty({
    type: String,
    nullable: true,
    description: 'The name of the person who assigned the request',
    example: '<PERSON>',
    name: 'assigner',
  })
  assigner: string;

  @AutoMap()
  @ApiProperty({
    type: Number,
    nullable: true,
    description: 'The ID of the person to whom the request is assigned',
    example: 12345,
    name: 'assignee',
  })
  assignee: number;

  @AutoMap()
  @ApiProperty({
    type: Date,
    nullable: true,
    description: 'The date when the request was assigned',
    example: '2023-10-01T12:00:00Z',
    name: 'dateAssigned',
  })
  dateAssigned: Date;

  @AutoMap()
  @ApiProperty({
    type: Boolean,
    default: false,
    description: 'Indicates whether the request has been completed',
    example: true,
    name: 'completed',
  })
  @IsBoolean()
  completed: boolean;

  @AutoMap()
  @ApiProperty({
    type: Date,
    nullable: true,
    description: 'The date when the request was completed',
    example: '2023-10-01T12:00:00Z',
    name: 'completedDate',
  })
  completedDate: Date;

  @AutoMap()
  @ApiProperty({
    type: String,
    nullable: true,
    description: 'The name of the person who completed the request',
    example: 'Jane Smith',
    name: 'completedBy',
  })
  completedBy: string;

  @AutoMap()
  @ApiProperty({
    type: Boolean,
    default: false,
    description: 'Indicates whether the request has been collected',
    example: true,
    name: 'collected',
  })
  @IsBoolean()
  collected: boolean;

  @AutoMap()
  @ApiProperty({
    type: Date,
    nullable: true,
    description: 'The date when the request was collected',
    example: '2023-10-01T12:00:00Z',
    name: 'collectedDate',
  })
  collectedDate: Date;

  @AutoMap()
  @ApiProperty({
    type: String,
    nullable: true,
    description: 'The name of the person who collected the request',
    example: 'Alice Johnson',
    name: 'collectedBy',
  })
  collectedBy: string;
}
