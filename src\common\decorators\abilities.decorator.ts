import { UserActions } from '../enums/user-actions.enum';
import { Subjects } from '../../core/security/authorization/casl-ability-factory/casl-ability-factory.service';
import { SetMetadata } from '@nestjs/common';

export interface RequiredRule {
  action: UserActions;
  subject: Subjects;
}

export const CHECK_ABILITY = 'check_ability';
// Custom decorator to set action and subject metadata
export const CheckAbilities = (...requirements: Array<RequiredRule>) =>
  SetMetadata(CHECK_ABILITY, requirements);
