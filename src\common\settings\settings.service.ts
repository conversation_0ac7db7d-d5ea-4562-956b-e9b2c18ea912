import { <PERSON>per } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Settings } from './entities/settings.entity';
import { OwnerType } from '../enums/owner-type.enum';
import { EntityStatus } from '../entities/base.entity';
import { UserService } from '../../core/security/user/user.service';
import { User } from '../../core/security/user/entities/user.entity';

@Injectable()
export class SettingsService {
  constructor(
    @InjectRepository(Settings)
    private settingsRepository: Repository<Settings>,
    @InjectMapper() private readonly classMapper: Mapper,
    private readonly userService: UserService,
  ) {}

  // async create(settings: Settings): Promise<Settings> {
  //   // run create validation here
  //   return this.settingsRepository.save(settings);
  // }
  //
  // async findAll(): Promise<Settings[]> {
  //   return this.settingsRepository.find();
  // }
  //
  // async findById(id: number): Promise<Settings> {
  //   return this.settingsRepository.findOneBy({ id });
  // }
  //
  // async findByOwnerId(ownerId: number): Promise<Settings> {
  //   return this.settingsRepository.findOneBy({ ownerId });
  // }
  //
  // async findByKeyAndOwnerId(key: string, ownerId: number): Promise<Settings> {
  //   return this.settingsRepository.findOneBy({ key, ownerId });
  // }
  //
  // async editAccountSettings(body, key): Promise<Settings | undefined> {
  //   let setting = await this.findByKeyAndOwnerId(key, body.userId);
  //   let object;
  //   if (!setting) {
  //     setting = new Settings();
  //     setting.key = key;
  //     object = {
  //       language: body.language,
  //       theme: body.theme,
  //     };
  //     setting.value = JSON.stringify(object);
  //     setting.ownerId = body.userId;
  //     setting.ownerType = OwnerType.USER;
  //     await this.updateUserStatus(body.userId, body.status);
  //     return await this.create(setting);
  //   }
  //   object = {
  //     language: body.language,
  //     theme: body.theme,
  //   };
  //   setting.value = JSON.stringify(object);
  //   await this.updateUserStatus(body.userId, body.status);
  //   return await this.update(setting);
  // }
  //
  // async updateUserStatus(userId, status: string): Promise<User> {
  //   const user = await this.userService.findById(userId);
  //   if (status === 'A') {
  //     user.status = EntityStatus.ACTIVE;
  //   } else {
  //     user.status = EntityStatus.INACTIVE;
  //   }
  //   return await this.userService.update(user);
  // }
  //
  // async update(settings: Settings): Promise<Settings> {
  //   // run update validation here
  //   return this.settingsRepository.save(settings);
  // }
  //
  // async remove(id: number): Promise<void> {
  //   await this.settingsRepository.delete(id);
  // }
}
