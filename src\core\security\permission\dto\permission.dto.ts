import { EntityDto } from '../../../../common/dto/base.dto';
import { AutoMap } from '@automapper/classes';
import { IsNotEmpty, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class PermissionDto extends EntityDto {
  @AutoMap()
  @IsNotEmpty()
  @IsString()
  @ApiProperty()
  name: string;

  @AutoMap()
  @IsNotEmpty()
  @IsString()
  @ApiProperty()
  description?: string;
}
