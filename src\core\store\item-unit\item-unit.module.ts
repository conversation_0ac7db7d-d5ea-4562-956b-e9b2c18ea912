import { AutomapperModule } from '@automapper/nestjs';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ItemUnit } from './entities/item-unit.entity';
import { ItemUnitController } from './item-unit.controller';
import { ItemUnitMapperService } from './item-unit.mapper.service';
import { ItemUnitService } from './item-unit.service';

@Module({
  controllers: [ItemUnitController],
  exports: [ItemUnitService],
  imports: [TypeOrmModule.forFeature([ItemUnit]), AutomapperModule],
  providers: [ItemUnitService, ItemUnitMapperService],
})
export class ItemUnitModule {}
