import { config } from 'dotenv';
import { firebaseConfig, FirebaseConfig } from './firebase';
import { dbConfig } from './database';
import { MailerConfig, mailerConfig } from './mail';
import {
  cloudinaryConfig,
  gcsConfig,
  s3Config,
  StorageOptions,
} from './storage';
import { PostgresConnectionOptions } from 'typeorm/driver/postgres/PostgresConnectionOptions';
import { twilioConfig, TwilioConfig } from './twilio';

config();

interface iConfig {
  approvalPath: string;
  backOfficeUrl: string;
  database: PostgresConnectionOptions;
  env: string;
  frontOfficeUrl: string;
  keys: {
    privateKey: string;
    publicKey: string;
    secret: string;
  };
  mails: MailerConfig;
  port: number;
  // firebase: FirebaseConfig;
  returnFormPath: string;
  storage: StorageOptions;
  twilio: TwilioConfig;
  userOnboardingPath: string;
  passwordResetPath: string;
}

export default (): Partial<iConfig> => ({
  env: process.env.NODE_ENV || 'development',
  port: parseInt(process.env.PORT, 10) || 3010,
  frontOfficeUrl: process.env.FRONT_OFFICE_BASE_URL || 'http://localhost',
  backOfficeUrl: process.env.BACK_OFFICE_BASE_URL || 'http://localhost',
  approvalPath: process.env.APPROVAL_PATH,
  userOnboardingPath: process.env.USER_ONBOARD_PATH,
  returnFormPath: process.env.RETURN_FORM_PATH,
  passwordResetPath: process.env.PASSWORD_RESET_PATH,
  keys: {
    privateKey: process.env.PRIVATE_KEY.replace(/\\n/gm, '\n'),
    publicKey: process.env.PUBLIC_KEY.replace(/\\n/gm, '\n'),
    secret: process.env.SECRET,
  },
  database: dbConfig(),
  mails: mailerConfig(),
  // firebase: firebaseConfig(),
  storage: {
    s3: s3Config(),
    gcs: gcsConfig(),
    cloudinary: cloudinaryConfig(),
  },
  twilio: twilioConfig(),
});
