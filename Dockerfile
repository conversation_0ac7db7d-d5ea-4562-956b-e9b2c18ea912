# Use the official Node.js image as the base image
FROM node:18
LABEL authors="VICTOR"

# Set the working directory in the container
WORKDIR /usr/src/app

# Copy package files and install dependencies
COPY package*.json ./
RUN npm install --legacy-peer-deps

# Copy the rest of the project files into the container
COPY . .

# Build the NestJS application
RUN npm run build

# Expose the port the app runs on
EXPOSE 4000

# Run the application
CMD ["npm", "run", "start:prod"]