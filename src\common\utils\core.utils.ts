import * as _ from 'lodash';
import path from 'path';
import fs from 'fs';
import { dynamicTemplate } from 'dynamic-string';
import {
  HttpException,
  HttpStatus,
  InternalServerErrorException,
} from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import * as bcrypt from 'bcryptjs';
import { Request } from 'express';

export type HandleRequestResult = {
  message: string;
  data?: object | string | boolean;
};

export class CoreUtils {
  static makeId(length: number): string {
    let result = '';
    const characters =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const charactersLength = characters.length;
    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }
    return result;
  }

  static formatBytes(bytes, decimals = 2): string {
    if (!+bytes) return '0 Bytes';

    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${sizes[i]}`;
  }

  static addMinutesToDate(date: Date, min: number): Date {
    date = new Date(date.getTime() + min * 60 * 1000);
    return date;
  }

  /**
   * This function extracts values from a string and returns an array of strings containing the extracted values. The input string is expected to contain comma-separated values.
   * @param {string} input - The input string from which values are to be extracted.
   * @returns {Array<string>} - An array of strings containing the extracted values
   */
  static extractValues(input: string): Array<string> {
    const values: Array<string> = [];
    const segments = input.split(',');
    segments.forEach((segment) => {
      const trimmedSegment = segment.trim();
      if (!_.isEmpty(trimmedSegment)) values.push(trimmedSegment);
    });
    return values;
  }

  /**
   * Separates a string into individual values using a specified delimiter.
   * @param input {string} - The input string to be separated into values.
   * @returns {string[]} - An array of strings containing the separated values.
   */
  static splitByPipe(input: string): string[] {
    return input.split('|').map((value) => value.trim());
  }

  static splitTakeLast(input: string, delimiter: string): string {
    return input.split(delimiter).pop();
  }

  static addSecondsToCurrentDate(seconds: number): Date {
    const currentDate = new Date();

    currentDate.setSeconds(currentDate.getSeconds() + seconds);
    return currentDate;
  }

  /**
   * This function checks if a token has expired.
   * @param {number} time - The time to be checked.
   */

  static hasExpired(time: number): boolean {
    return time < Date.now();
  }

  /**
   * Generate OTP
   * @param length
   * @returns string
   */
  static otpGenerator = (length: number): string => {
    const charset = '0123456789';
    let otp = '';
    for (let i = 0; i < length; i++) {
      const random = Math.floor(Math.random() * charset.length);
      otp += charset[random];
    }
    return otp;
  };

  /**
   * Get enum values from enum file
   * @param enumName
   * @returns { value: string; label: string }[]
   */
  static async getEnumValues(
    enumName: string,
  ): Promise<{ value: string; label: string }[]> {
    // Determine if we're in the dist or src directory
    const distPath = path.resolve(__dirname, 'enums', `${enumName}.enum.js`);
    const srcPath = path.resolve(
      __dirname,
      '..',
      'src',
      'enums',
      `${enumName}.enum.ts`,
    );

    let enumPath: string;

    if (fs.existsSync(distPath)) {
      enumPath = distPath;
    } else if (fs.existsSync(srcPath)) {
      enumPath = srcPath;
    } else {
      throw new Error(`Enum with name ${enumName} not found`);
    }

    // Dynamically import the enum module
    const enumModule = await import(enumPath);
    const enumObj = enumModule[enumName];
    if (!enumObj) {
      throw new Error(`Enum with name ${enumName} not found in module`);
    }

    return Object.keys(enumObj).map((key) => ({
      value: enumObj[key],
      label: key,
    }));
  }

  /**
   * Build success response
   * @param error
   * @param content
   * @param statusCode
   * @returns any
   */
  static buildFailureResponse(
    error: string,
    content?: any,
    statusCode = 401,
  ): any {
    return {
      status: statusCode,
      content: {
        msg: content ? dynamicTemplate(error, content) : error,
      },
    };
  }

  /**
   * Generate random password
   * @param length
   */
  static generateRandomPassword(length = 12): string {
    const upperCaseLetters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const lowerCaseLetters = 'abcdefghijklmnopqrstuvwxyz';
    const digits = '0123456789';
    const specialCharacters = '!@#$%^&*()_+[]{}|;:,.<>?';

    const allCharacters =
      upperCaseLetters + lowerCaseLetters + digits + specialCharacters;
    let password = '';

    for (let i = 0; i < length; i++) {
      const randomIndex = Math.floor(Math.random() * allCharacters.length);
      password += allCharacters[randomIndex];
    }

    // Ensure the password contains at least one character from each character set
    password = this.replaceAt(
      password,
      Math.floor(Math.random() * length),
      upperCaseLetters,
    );
    password = this.replaceAt(
      password,
      Math.floor(Math.random() * length),
      lowerCaseLetters,
    );
    password = this.replaceAt(
      password,
      Math.floor(Math.random() * length),
      digits,
    );
    password = this.replaceAt(
      password,
      Math.floor(Math.random() * length),
      specialCharacters,
    );

    return password;
  }

  /**
   * Replace a character at a specific index in a string with a random character from a specified character set (string).
   * @param str
   * @param index
   * @param characters
   */
  static replaceAt(str: string, index: number, characters: string): string {
    const randomCharacter =
      characters[Math.floor(Math.random() * characters.length)];
    return str.slice(0, index) + randomCharacter + str.slice(index + 1);
  }

  static genericResponse(
    response: any,
    status: HttpStatus,
    message: string,
    data: any = null,
  ) {
    const responseData: any = {
      statusCode: status,
      message: message,
    };

    if (data) {
      responseData.data = data;
    }

    return response.status(status).json(responseData);
  }

  static getLastName(fullName: string): string {
    const parts = fullName.split(' ');
    return parts[parts.length - 1];
  }

  static stringToNumber(value: string): number {
    return Number(value);
  }

  static generateToken(): string {
    const uuid: string = uuidv4();
    return uuid.toUpperCase();
  }

  /**
   * Throws an error with the appropriate status code.
   * @param error
   */
  static throwError(error: any) {
    throw error.status == undefined
      ? new InternalServerErrorException(error?.message)
      : new HttpException(error?.message, error.status);
  }

  /**
   * Handles the request and returns the response.
   * @param action
   */
  static async handleRequest(action: () => Promise<HandleRequestResult>) {
    try {
      return await action();
    } catch (error) {
      CoreUtils.throwError(error);
    }
  }

  static hash(value: string): string {
    return bcrypt.hashSync(value, 10);
  }

  static compare(value: string, hash: string): boolean {
    return bcrypt.compareSync(value, hash);
  }

  static getCurrentRoute(req: Request): string {
    return `${req.protocol}://${req.get('host')}${
      req.originalUrl.split('?')[0]
    }`;
  }

  /**
   * Calculates the duration between now and the return date
   * @returns A Date object representing the duration
   * @param dateOfReturn
   */
  static calculateRemainingDuration(dateOfReturn: Date): Date {
    const currentDate = new Date();
    const remainingDuration = new Date(
      dateOfReturn.getTime() - currentDate.getTime(),
    );
    return remainingDuration;
  }

  /**
   * Generates a unique serial number for items based on department, ministry, and item name
   * @param departmentName - The name of the department
   * @param itemName - The name of the item
   * @param ministryName - The name of the ministry (defaults to 'EGFM')
   * @param count - The current count for this item (defaults to 0)
   * @returns A formatted serial number string (e.g., "AUD/EGFM/SPE/001")
   */
  static generateSerialNumber(
    departmentName: string,
    itemName: string,
    ministryName = 'EGFM',
    count = 0,
  ): string {
    // Get the first 3 letters of the department name and convert to uppercase
    const deptPrefix = departmentName.substring(0, 3).toUpperCase();

    // Get the first 3 letters of the ministry name and convert to uppercase
    const ministryPrefix = ministryName.substring(0, 4).toUpperCase();

    // Get the first 3 letters of the item name and convert to uppercase
    const itemPrefix = itemName.substring(0, 3).toUpperCase();

    // Format the number with leading zeros
    const formattedNumber = (count + 1).toString().padStart(3, '0');

    // Combine all parts with forward slashes
    return `${ministryPrefix}/${deptPrefix}/${itemPrefix}/${formattedNumber}`;
  }
}
