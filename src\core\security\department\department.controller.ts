import { CurrentRoute } from '@common/decorators/current-route.decorator';
import { CurrentUser } from '@common/decorators/current-user.decorator';
import { Public } from '@common/decorators/public.decorator';
import { CoreUtils } from '@common/utils/core.utils';
import { AbilityGuard } from '@core/security/authorization/ability/ability.guard';
import { User } from '@core/security/user/entities/user.entity';
import {
  Body,
  Controller,
  DefaultValuePipe,
  Get,
  HttpStatus,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBody,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { Pagination } from 'nestjs-typeorm-paginate';
import { DepartmentService } from './department.service';
import { ActivateDepartmentDto } from './dto/activate-department-log.dto';
import { CreateDepartmentDto } from './dto/create-department.dto';
import { DeactivateDepartmentDto } from './dto/deactivate-department-log.dto';
import { DepartmentDto } from './dto/department.dto';
import { UpdateDepartmentDto } from './dto/update-department.dto';

@UseGuards(AbilityGuard)
@ApiTags('Department')
@Controller({
  path: 'department',
  version: '1',
})
export class DepartmentController {
  constructor(private readonly departmentService: DepartmentService) {}

  @ApiOperation({ summary: 'Create a new department' })
  @ApiBody({ type: CreateDepartmentDto })
  @Post('new')
  async new(
    @Body() createDepartmentDto: CreateDepartmentDto,
    @CurrentUser() user: User,
  ) {
    return CoreUtils.handleRequest(async () => {
      await this.departmentService.createDepartment(createDepartmentDto, user);
      return {
        message: 'Department created successfully',
        data: null,
      };
    });
  }

  @ApiOperation({ summary: 'Update a department' })
  @ApiBody({ type: UpdateDepartmentDto })
  @Patch('update/:departmentId')
  async update(
    @Param('departmentId') id: number,
    @Body() updateDepartmentDto: DepartmentDto,
    @CurrentUser() user: User,
  ) {
    return CoreUtils.handleRequest(async () => {
      await this.departmentService.updateDepartment(
        id,
        updateDepartmentDto,
        user,
      );
      return {
        message: 'Department updated successfully',
        data: null,
      };
    });
  }

  @ApiOperation({ summary: 'Retrieve a department' })
  @Get('detail/:departmentId')
  async retrieveADepartment(@Param('departmentId') id: number) {
    return CoreUtils.handleRequest(async () => {
      const data = await this.departmentService.getDepartment(id);
      return {
        message: 'Department retrieved successfully',
        data,
      };
    });
  }

  @ApiOperation({ summary: 'Activate a department' })
  @ApiBody({ type: ActivateDepartmentDto })
  @Patch('activate')
  async activate(@Body() body: ActivateDepartmentDto) {
    return CoreUtils.handleRequest(async () => {
      await this.departmentService.activate(body.ids);
      return {
        message: `Department${
          body.ids.length > 1 ? 's' : ''
        } activated successfully`,
        data: null,
      };
    });
  }

  @ApiOperation({ summary: 'Deactivate a department' })
  @ApiBody({ type: DeactivateDepartmentDto })
  @Patch('deactivate')
  async deactivate(@Body() body: DeactivateDepartmentDto) {
    return CoreUtils.handleRequest(async () => {
      await this.departmentService.deactivate(body.ids);
      return {
        message: `Department${
          body.ids.length > 1 ? 's' : ''
        } deactivated successfully`,
        data: null,
      };
    });
  }

  @Get()
  @ApiOperation({ summary: 'Get paginated list of departments' })
  @ApiQuery({ name: 'page', required: true, type: Number, example: 1 })
  @ApiQuery({ name: 'limit', required: true, type: Number, example: 10 })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'filter', required: false, type: String })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Successful response with paginated requests',
    type: Pagination,
  })
  async getPaginatedDepartments(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit,
    @Query('search') search: string,
    @Query('filter') filter: string,

    @CurrentRoute() route: string,
  ) {
    return CoreUtils.handleRequest(async () => {
      const data = await this.departmentService.getPaginatedDepartments(
        {
          page,
          limit,
          search,
          filter,
        },
        route,
      );
      return {
        message: 'Departments retrieved successfully',
        data,
      };
    });
  }

  @Public()
  // @CheckAbilities({ action: UserActions.READ, subject: Department })
  @ApiOperation({ summary: 'Get all departments' })
  @Get('all')
  @ApiOkResponse({
    description: 'Departments retrieved successfully',
    type: DepartmentDto,
    isArray: true,
  })
  async allDepartments() {
    return CoreUtils.handleRequest(async () => {
      const data = await this.departmentService.fetchAllDepartmentNames();
      return {
        message: 'Departments retrieved successfully',
        data,
      };
    });
  }
}
