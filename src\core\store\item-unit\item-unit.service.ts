import { Injectable } from '@nestjs/common';
import { CreateItemUnitDto } from './dto/create-item-unit.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ItemUnit } from './entities/item-unit.entity';
import { InjectMapper } from '@automapper/nestjs';
import { Mapper } from '@automapper/core';

@Injectable()
export class ItemUnitService {
  constructor(
    @InjectRepository(ItemUnit)
    private readonly itemUnitRepository: Repository<ItemUnit>,
  ) {}

  async findByPk(id: number): Promise<ItemUnit> {
    return this.itemUnitRepository.findOneBy({ id });
  }
}
