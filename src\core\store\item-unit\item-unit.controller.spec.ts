import { Test, TestingModule } from '@nestjs/testing';
import { ItemUnitController } from './item-unit.controller';
import { ItemUnitService } from './item-unit.service';

describe('ItemUnitController', () => {
  let controller: ItemUnitController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ItemUnitController],
      providers: [ItemUnitService],
    }).compile();

    controller = module.get<ItemUnitController>(ItemUnitController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
