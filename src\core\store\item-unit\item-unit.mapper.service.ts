import {
  createMap,
  forMember,
  mapFrom,
  Mapper,
  MappingProfile,
  ignore,
} from '@automapper/core';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import { ItemUnitDto } from './dto/item-unit.dto';
import { ItemUnit } from './entities/item-unit.entity';
import { UpdateItemUnitDto } from './dto/update-item-unit.dto';

@Injectable()
export class ItemUnitMapperService extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile(): MappingProfile {
    return (mapper) => {
      createMap(mapper, ItemUnit, ItemUnitDto);

      createMap(
        mapper,
        UpdateItemUnitDto,
        ItemUnit,
        forMember(
          (destination) => destination.store,
          mapFrom((source) => ({ id: source.storeId })),
        ),
      );
    };
  }
}
