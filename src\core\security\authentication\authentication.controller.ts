import { CurrentUser } from '@common/decorators/current-user.decorator';
import { Public } from '@common/decorators/public.decorator';
import { CoreUtils } from '@common/utils/core.utils';
import { MailService } from '@core/notification/mail-service/mail.service';
import {
  Body,
  Controller,
  HttpCode,
  HttpStatus,
  Patch,
  Post,
  Res,
} from '@nestjs/common';
import { ApiBody, ApiOperation, ApiTags } from '@nestjs/swagger';
import { Throttle } from '@nestjs/throttler';
import { RegenerateTokenDto } from '../user/dto/regenerate-token.dto';
import { VerifyUserDto } from '../user/dto/verify-user.dto';
import { UserService } from '../user/user.service';
import { AuthenticationService } from './authentication.service';
import { ChangePasswordDto } from './dto/change-password.dto';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { LoginDto } from './dto/login.dto';
import { OnboardUserDto } from './dto/onboard-user.dto';
import { PasswordResetDto } from './dto/password.reset.dto';
import { LoggerService } from '@common/logger/logger.service';

@ApiTags('Authentication')
@Controller({
  path: 'authentication',
  version: '1',
})
export class AuthenticationController {
  constructor(
    private readonly authenticationService: AuthenticationService,
    private readonly userService: UserService,
    private readonly mailService: MailService,
    private readonly logger: LoggerService,
  ) {
    this.logger.setContext(AuthenticationController.name);
  }

  @ApiOperation({ summary: 'Login' })
  @Throttle({})
  @Public()
  @Post('login')
  @HttpCode(HttpStatus.OK)
  @ApiBody({ type: LoginDto })
  async login(@Body() loginDto: LoginDto): Promise<any> {
    return CoreUtils.handleRequest(async () => {
      const { email, password } = loginDto;
      const auth = await this.authenticationService.signin(email, password);
      return { message: 'Login successful', data: auth };
    });
  }

  @ApiOperation({ summary: 'Register' })
  @Public()
  @Post('register')
  @HttpCode(HttpStatus.CREATED)
  @ApiBody({ type: OnboardUserDto })
  async register(@Body() onboardUserDto: OnboardUserDto) {
    return CoreUtils.handleRequest(async () => {
      const auth = await this.authenticationService.signup(onboardUserDto);
      return {
        message: 'Registration successful',
        data: auth,
      };
    });
  }

  @Public()
  @ApiOperation({ summary: 'Verify a user' })
  @ApiBody({ type: VerifyUserDto })
  @Patch('verification')
  async verifyUser(@Body() verifyUserDto: VerifyUserDto) {
    return CoreUtils.handleRequest(async () => {
      const data = await this.authenticationService.verifyUser(verifyUserDto);
      return {
        message: 'User verified successfully',
        data,
      };
    });
  }

  @Public()
  @ApiOperation({ summary: 'Regenerate token and resend verification email' })
  @ApiBody({ type: RegenerateTokenDto })
  @Patch('verification/resend')
  async updateTokenAndResendVerificationEmail(
    @Body() body: RegenerateTokenDto,
  ) {
    return CoreUtils.handleRequest(async () => {
      await this.userService.updateTokenAndResendVerificationEmail(body.email);
      return {
        message: 'Token regenerated and email sent successfully',
        data: null,
      };
    });
  }

  @ApiOperation({ summary: 'Change Password' })
  @ApiBody({ type: ChangePasswordDto })
  @Patch('change-password')
  async changePassword(
    @Body() body: ChangePasswordDto,
    @CurrentUser('id') userId: number,
  ) {
    return CoreUtils.handleRequest(async () => {
      await this.authenticationService.changePassword(body, userId);
      return {
        message: 'Password changed successfully.',
        data: null,
      };
    });
  }

  @ApiOperation({ summary: 'Reset Password' })
  @Public()
  @Post('password-reset')
  @ApiBody({ type: PasswordResetDto })
  async resetPassword(
    @Res() response: any,
    @Body() passwordResetDto: PasswordResetDto,
  ) {
    return CoreUtils.handleRequest(async () => {
      const existingUser = await this.userService.fetchUserByToken(
        passwordResetDto.token,
      );

      if (
        !existingUser &&
        existingUser.password !== passwordResetDto.defaultPassword
      ) {
        return CoreUtils.genericResponse(
          response,
          HttpStatus.NOT_FOUND,
          'Token and default password does not belong to this user',
        );
      }
      await this.userService.update(
        await this.authenticationService.resetPassword(
          passwordResetDto.newPassword,
          existingUser,
        ),
      );
      return {
        message: 'Password reset successful',
        data: null,
      };
    });
  }

  @ApiOperation({ summary: 'Forgot Password' })
  @Public()
  @Post('forgot-password')
  @ApiBody({ type: ForgotPasswordDto })
  async forgotPassword(
    // @Res() response: any,
    @Body() forgotPasswordDto: ForgotPasswordDto,
  ) {
    return CoreUtils.handleRequest(async () => {
      await this.authenticationService.forgotPassword(forgotPasswordDto.email);
      return {
        message: 'Reset Password link sent successful',
        data: null,
      };
    });
    // try {
    //   const user = await this.userService.findByEmail(forgotPasswordDto.email);
    //   if (!user) {
    //     return CoreUtils.genericResponse(
    //       response,
    //       HttpStatus.NOT_FOUND,
    //       `user with email ${forgotPasswordDto.email} not found`,
    //     );
    //   } else {
    //     const resetPasswordUrl =
    //       await this.userService.generatePasswordResetUrl();
    //     await this.mailService.sendSmtpMail(
    //       `${forgotPasswordDto.email}`,
    //       'Password Reset',
    //       `
    //       <p>Click the link below to reset your password</p>
    //       <a href="${resetPasswordUrl}/">Reset Password</a>
    //       `,
    //     );
    //     return {
    //       message: 'Reset Password link sent successful',
    //       data: null,
    //     };
    //   }
    // } catch (err) {
    //   return CoreUtils.genericResponse(
    //     response,
    //     HttpStatus.BAD_REQUEST,
    //     `${err.message}`,
    //   );
    // }
  }
}
