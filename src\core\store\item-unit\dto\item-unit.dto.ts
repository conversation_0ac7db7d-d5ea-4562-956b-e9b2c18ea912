import { AutoMap } from '@automapper/classes';
import { StoreDto } from '@core/store/dto/store.dto';
import { ApiProperty } from '@nestjs/swagger';

export class ItemUnitDto {
  @AutoMap(() => Number)
  @ApiProperty()
  id: number;

  @AutoMap()
  @ApiProperty()
  serialNumber: string;

  @AutoMap()
  @ApiProperty()
  condition: string;

  @AutoMap(() => StoreDto)
  @ApiProperty()
  store: StoreDto;
}
