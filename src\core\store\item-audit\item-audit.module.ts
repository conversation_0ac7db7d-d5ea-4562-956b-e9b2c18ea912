import { Modu<PERSON> } from '@nestjs/common';
import { ItemAuditService } from './item-audit.service';
import { ItemAuditController } from './item-audit.controller';
import { ItemAuditValidator } from './item-audit.validator';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AutomapperModule } from '@automapper/nestjs';
import { ItemAudit } from './entities/item-audit.entity';
import { ItemAuditMapper } from './item-audit.mapper.service';

@Module({
  imports: [TypeOrmModule.forFeature([ItemAudit]), AutomapperModule],
  controllers: [ItemAuditController],
  providers: [ItemAuditService, ItemAuditValidator, ItemAuditMapper],
  exports: [ItemAuditService],
})
export class ItemAuditModule {}
