import { Condition } from '@common/enums/condition.enum';
import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';

export class ItemConditionStatusUpdateDto {
  @AutoMap(() => String)
  @ApiProperty({
    description: 'Possible conditions of an item',
    enum: Object.values(Condition),
    name: 'condition',
    type: 'string',
    default: Condition.NOT_SPECIFIED,
  })
  condition: Condition;
}
