import { AutoMap } from '@automapper/classes';
import { AbstractEntity } from '@common/entities/base.entity';
import { ItemUnit } from '@core/store/item-unit/entities/item-unit.entity';
import { Column, Entity, JoinColumn, ManyToOne, OneToMany } from 'typeorm';
import { Department } from '@core/security/department/entities/department.entity';

@Entity({ name: 'item' })
export class Item extends AbstractEntity {
  @AutoMap()
  @Column({ name: 'name', type: 'varchar', unique: true })
  name: string;

  @AutoMap()
  @Column({ name: 'available_quantity', default: 0, type: 'bigint' })
  availableQuantity: number;

  @AutoMap()
  @Column({ name: 'actual_quantity', default: 0, type: 'bigint' })
  actualQuantity: number;

  @AutoMap()
  @Column({ name: 'fragile', type: 'boolean' })
  fragile: boolean;

  @AutoMap()
  @Column({ name: 'picture_url', type: 'varchar' })
  pictureUrl?: string;

  @AutoMap(() => ItemUnit)
  @OneToMany(() => ItemUnit, (itemUnit) => itemUnit.item, {
    eager: true,
    cascade: true,
  })
  itemUnits: ItemUnit[];

  @AutoMap(() => Department)
  @ManyToOne(() => Department, (department) => department.items, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'department_id' })
  department: Department;
}
