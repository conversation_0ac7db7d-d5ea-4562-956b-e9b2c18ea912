import { AutoMap } from '@automapper/classes';
import { AbstractEntity } from '@common/entities/base.entity';
import { IsEmail } from 'class-validator';
import { Column, Entity, OneToOne } from 'typeorm';
import { ComplaintSummary } from '@core/complaint/complaint-summary/entities/complaint-summary.entity';

@Entity({ name: 'complaint' })
export class Complaint extends AbstractEntity {
  @AutoMap()
  @Column({ name: 'complainer_name', type: 'varchar' })
  complainerName: string;

  @AutoMap()
  @Column({ name: 'complainer_phone', type: 'varchar' })
  complainerPhone: string;

  @AutoMap()
  @Column({ name: 'complaint_subject', type: 'varchar' })
  complaintSubject: string;

  @AutoMap()
  @IsEmail()
  @Column({ name: 'complainer_email', type: 'varchar' })
  complainerEmail: string;

  @AutoMap()
  @Column({ name: 'complaint_description', type: 'text' })
  complaintDescription: string;

  @AutoMap()
  @Column({ name: 'complaint_date', type: 'timestamp' })
  complaintDate: Date;

  @AutoMap()
  @OneToOne(() => ComplaintSummary, (summary) => summary.complaint, {
    eager: true,
    cascade: true,
    onDelete: 'CASCADE',
    orphanedRowAction: 'delete',
    onUpdate: 'CASCADE',
  })
  summary: ComplaintSummary;
}
