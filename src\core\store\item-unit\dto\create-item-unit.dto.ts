import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsNumber, IsString } from 'class-validator';
export class CreateItemUnitDto {
  @AutoMap()
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  itemId: string;

  @AutoMap()
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  serialNumber: string;

  @AutoMap()
  @ApiProperty()
  @IsNumber()
  quantity: number;
}
