import {
  MigrationInterface,
  QueryRunner,
  Table,
  TableForeignKey,
  TableIndex,
} from 'typeorm';

export class CreateComplaintComplaintSummaryFk1752663486437
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'complaint',
        columns: [
          {
            name: 'id',
            type: 'bigint',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          { name: 'complainer_name', type: 'varchar' },
          { name: 'complainer_phone', type: 'varchar' },
          { name: 'complainer_email', type: 'varchar' },
          { name: 'complaint_subject', type: 'varchar' },
          { name: 'complaint_description', type: 'text' },
          { name: 'complaint_date', type: 'timestamp' },
          {
            name: 'status',
            type: 'varchar',
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'now()',
          },
          {
            name: 'created_by',
            type: 'varchar',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'updated_by',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'deleted_at',
            type: 'timestamp',
            isNullable: true,
          },
        ],
      }),
      true,
    );

    // Add an index to the table.
    await queryRunner.createIndex(
      'complaint',
      new TableIndex({
        name: 'IDX_COMPLAINT',
        columnNames: ['complainer_name', 'complainer_email'],
      }),
    );

    // Create complaint_summary table
    await queryRunner.createTable(
      new Table({
        name: 'complaint_summary',
        columns: [
          {
            name: 'id',
            type: 'bigint',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
            { name: 'complaint_status', type: 'varchar' },
            { name: 'attended_to', type: 'boolean', default: false },
            { name: 'date_resolved', type: 'timestamp', isNullable: true },
            { name: 'resolved_by', type: 'varchar', isNullable: true },
            { name: 'complaint_id', type: 'bigint', isNullable: true },
          {
            name: 'status',
            type: 'varchar',
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'now()',
          },
          {
            name: 'created_by',
            type: 'varchar',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'updated_by',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'deleted_at',
            type: 'timestamp',
            isNullable: true,
          },
        ],
      }),
      true,
    );

    await queryRunner.createIndex(
      'complaint_summary',
      new TableIndex({
        name: 'IDX_COMPLAINT_SUMMARY',
        columnNames: ['complaint_status', 'date_resolved'],
      }),
    );

    await queryRunner.createForeignKey(
      'complaint_summary',
      new TableForeignKey({
        columnNames: ['complaint_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'complaint',
        onDelete: 'CASCADE',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop FK
    const complaintSummaryTable = await queryRunner.getTable(
      'complaint_summary',
    );
    const fk = complaintSummaryTable.foreignKeys.find(
      (fk) => fk.columnNames.indexOf('complaint_id') !== -1,
    );
    await queryRunner.dropForeignKey('complaint_summary', fk);

    // Drop index
    await queryRunner.dropIndex('complaint_summary', 'IDX_COMPLAINT_SUMMARY');

    // Drop table
    await queryRunner.dropTable('complaint_summary');

    // Drop the index first before dropping the table.
    await queryRunner.dropIndex('complaint', 'IDX_COMPLAINT');
    await queryRunner.dropTable('complaint');
  }
}
