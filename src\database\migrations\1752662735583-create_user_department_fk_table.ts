import {
  MigrationInterface,
  QueryRunner,
  <PERSON><PERSON><PERSON>umn,
  TableForeignKey,
} from 'typeorm';

export class CreateUserDepartmentFkTable1752662735583
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'user',
      new TableColumn({
        isNullable: true,
        name: 'department_id',
        type: 'bigint',
      }),
    );

    await queryRunner.createForeign<PERSON>ey(
      'user',
      new TableForeignKey({
        columnNames: ['department_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'department',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const userTable = await queryRunner.getTable('user');
    const fk = userTable.foreignKeys.find(
      (fk) => fk.columnNames.indexOf('department_id') !== -1,
    );
    await queryRunner.dropForeignKey('user', fk);
    await queryRunner.dropColumn('user', 'department_id');
  }
}
