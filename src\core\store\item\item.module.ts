import { Modu<PERSON> } from '@nestjs/common';
import { ItemService } from './item.service';
import { ItemController } from './item.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Item } from './entities/item.entity';
import { AutomapperModule } from '@automapper/nestjs';
import { ItemMapperService } from './item.mapper.service';
import { ItemValidatorService } from './item.validator.service';
import { StoreModule } from '@core/store/store.module';
import { DepartmentModule } from '@core/security/department/department.module';
import { ItemUnitModule } from '@core/store/item-unit/item-unit.module';
import { SecurityModule } from '@core/security/security.module';
import { NotificationModule } from '@core/notification/notification.module';

@Module({
  controllers: [ItemController],
  exports: [ItemService, ItemValidatorService],
  imports: [
    TypeOrmModule.forFeature([Item]),
    AutomapperModule,
    StoreModule,
    DepartmentModule,
    ItemUnitModule,
    NotificationModule,
    SecurityModule,
  ],
  providers: [ItemService, ItemMapperService, ItemValidatorService],
})
export class ItemModule {}
