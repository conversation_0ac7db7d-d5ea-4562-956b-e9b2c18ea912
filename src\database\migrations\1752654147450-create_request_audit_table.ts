import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateRequestAuditTable1752654147450
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create request_audit table
    await queryRunner.createTable(
      new Table({
        name: 'request_audit',
        columns: [
          {
            name: 'id',
            type: 'bigint',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'assigned',
            type: 'boolean',
            default: false,
          },
          {
            name: 'assigner',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'assignee',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'date_assigned',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'completed',
            type: 'boolean',
            default: false,
          },
          {
            name: 'completed_date',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'completed_by',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'collected',
            type: 'boolean',
            default: false,
          },
          {
            name: 'collected_date',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'collected_by',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'status',
            type: 'varchar',
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'now()',
          },
          {
            name: 'created_by',
            type: 'varchar',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'updated_by',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'deleted_at',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'request_id',
            type: 'bigint',
            isNullable: true,
          },
        ],
      }),
      true,
    );

    /**
     * Create Index
     */
    // Add an index to the user table.
    await queryRunner.createIndex(
      'request_audit',
      new TableIndex({
        name: 'IDX_REQUEST_ASSIGNER',
        columnNames: [
          'assigner',
          'assignee',
          'date_assigned',
          'completed',
          'completed_date',
          'completed_by',
          'collected',
          'collected_date',
          'collected_by',
          'status',
          'created_at',
          'created_by',
          'updated_at',
          'updated_by',
          'deleted_at',
        ],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    //Drop user table and its index
    await queryRunner.dropIndex('request_audit', 'IDX_REQUEST_ASSIGNER');
    await queryRunner.dropTable('request_audit');
  }
}
