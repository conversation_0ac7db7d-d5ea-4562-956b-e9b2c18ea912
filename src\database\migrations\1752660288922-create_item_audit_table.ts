import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateItemAuditTable1752660288922 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'item_audit',
        columns: [
          {
            name: 'id',
            type: 'bigint',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'item_id',
            type: 'bigint',
            isNullable: true,
          },
          {
            name: 'item_name',
            type: 'varchar',
          },
          {
            name: 'quantity_leased_out',
            type: 'bigint',
            isNullable: true,
          },
          {
            name: 'leased_out_date',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'condition_before_leased_out',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'quantity_released',
            type: 'bigint',
            default: 0,
          },
          {
            name: 'released_date',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'quantity_returned',
            type: 'bigint',
            isNullable: true,
          },
          {
            name: 'returned_date',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'condition_after_leased_out',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'audit_completed',
            type: 'boolean',
            default: false,
          },
          {
            name: 'audit_completed_date',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'audit_collected',
            type: 'boolean',
            default: false,
          },
          {
            name: 'audit_collected_date',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'item_units',
            type: 'jsonb',
          },
          {
            name: 'status',
            type: 'varchar',
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'now()',
          },
          {
            name: 'created_by',
            type: 'varchar',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'updated_by',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'deleted_at',
            type: 'timestamp',
            isNullable: true,
          },
        ],
      }),
      true,
    );

    // Add an index to the table.
    await queryRunner.createIndex(
      'item_audit',
      new TableIndex({
        name: 'IDX_ITEM_AUDIT',
        columnNames: ['id', 'item_id', 'item_name', 'status'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropIndex('item_audit', 'IDX_STORE_NAME');
    await queryRunner.dropTable('item_audit');
  }
}
