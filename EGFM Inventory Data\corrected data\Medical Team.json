[{"name": "<PERSON><PERSON> 20mg", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 3}, {"name": "Em-vit C syrup 100ml", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 2}, {"name": "Emzolyn cough syrup", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 3}, {"name": "Emzolyn Expectorant", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 2}, {"name": "Moxie Paracetamol", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 1}, {"name": "Loratidine 5mg/ml", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 3}, {"name": "da<PERSON><PERSON>", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 14}, {"name": "Brustan-N (Ibuprofen)", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 1}, {"name": "Rulox Antacid Suspension", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 1}, {"name": "Diclofenac 50mg", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 1}, {"name": "omeprazole", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 11}, {"name": "2ml syringe", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 88}, {"name": "lodium", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 7}, {"name": "5ml syringe", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 69}, {"name": "intravenous fluid", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 22}, {"name": "Nebuliser", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 1}, {"name": "23G <PERSON><PERSON>", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 23}, {"name": "Cotton wool", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 1}, {"name": "Jucopan", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 19}, {"name": "21G Needle", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 23}, {"name": "Fluid giving set", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 9}, {"name": "10mls syringe", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 32}, {"name": "Loratidine 10mg", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 4}, {"name": "Disposable face mask", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 4}, {"name": "inhaler", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 2}, {"name": "Blue I.V Catheter", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 4}, {"name": "Prednisolone", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 8}, {"name": "Sars Cov2 antigen rapid test", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 2}, {"name": "Yellow I.V Catheter", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 7}, {"name": "Sanitizer spray", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 1}, {"name": "Pink I.V catheter", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 4}, {"name": "Deep Blue I.V Catheter", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 10}, {"name": "Sterogrip Bandage", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 1}, {"name": "Green I.V Catheter", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 10}, {"name": "Bioflor", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 24}, {"name": "Sphygmomanometer", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 3}, {"name": "metronidazole infusion", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 2}, {"name": "Ciprofoxacin infusion", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 1}, {"name": "Vitamin B Complex (I.V/I.M)", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 5}, {"name": "Arthemeter Injectable", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 4}, {"name": "ORS", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 2}, {"name": "Gloves", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 1}, {"name": "Injection Water", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 3}, {"name": "Ibruprofen", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 1}, {"name": "50% Glucose I.V Infusion", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 2}, {"name": "zinc", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 3}, {"name": "Diazepam (JUHEL)", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 9}, {"name": "Neurogesic ointment", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 1}, {"name": "Amoxycillin", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 9}, {"name": "NimARTEM QS", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 3}, {"name": "Ceftriazone Injection", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 1}, {"name": "Lidocaine 5mls", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 2}, {"name": "Fungusol cream", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 1}, {"name": "Ytacan Clotrimazole cream", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 1}, {"name": "Paracetamol", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 1}, {"name": "Penicillin cream", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 3}, {"name": "Lofnac cream", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 1}, {"name": "<PERSON><PERSON>am Injection", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 1}, {"name": "Danacid", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 0}, {"name": "Skineal cream", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 2}, {"name": "Borocare Neem Cream", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 1}, {"name": "Tribotan cream", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 1}, {"name": "Rexifen", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 7}, {"name": "Hydrocortisone cream", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 2}, {"name": "Mepisan cream", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 1}, {"name": "Face mask", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 1}, {"name": "Mycoten cream", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 1}, {"name": "Metronidazole", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 0}, {"name": "Glucometer strip", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 0}, {"name": "Nifedipine tablet 30mg", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 1}, {"name": "Diclofenac Sodium Injection", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 22}, {"name": "Chlorpromazine", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 8}, {"name": "Vitamin K3 injection", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 1}, {"name": "Amlodipine 10mg", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 2}, {"name": "Gloves", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 2}, {"name": "Bromazepam", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 2}, {"name": "<PERSON><PERSON><PERSON>", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 2}, {"name": "Aminophylline Injection", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 1}, {"name": "Paracetamol Injection", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 10}, {"name": "Spirit", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 2}, {"name": "Vitamin C tablet", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 1}, {"name": "Promethazine Hydrochloride Injection", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 13}, {"name": "Su<PERSON><PERSON><PERSON>", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 2}, {"name": "Antallarge eye drop", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 1}, {"name": "Eusol", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 4}, {"name": "Not specified", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 2}, {"name": "hydrogen peroxide", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 2}, {"name": "Hyoscine Butylbromide", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 1}, {"name": "Sanitizer", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 2}, {"name": "Povidone iodine", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 0}, {"name": "<PERSON><PERSON><PERSON>", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 1}, {"name": "Surgical gloves", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 2}, {"name": "Injection water", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 4}, {"name": "Thermometer", "condition": "Good", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 1}, {"name": "plaster", "condition": "Bad", "storeId": 2, "fragile": true, "departmentId": 6, "actualQuantity": 2}]