import { CurrentRoute } from '@common/decorators/current-route.decorator';
import { CurrentUser } from '@common/decorators/current-user.decorator';
import { CoreUtils } from '@common/utils/core.utils';
import { User } from '@core/security/user/entities/user.entity';
import { Body, Controller, Get, HttpStatus, NotFoundException, Param, Patch, Post, Query } from '@nestjs/common';
import { ApiBody, ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { ActivateStoreDto } from './dto/activate-store.dto';
import { CreateStoreDto } from './dto/create-store.dto';
import { DeactivateStoreDto } from './dto/deactivate-store.dto';
import { StoreDto } from './dto/store.dto';
import { StoreService } from './store.service';
import { LoggerService } from '@common/logger/logger.service';
import { InjectMapper } from '@automapper/nestjs';
import { Mapper } from '@automapper/core';
import { Store } from '@core/store/entities/store.entity';
import { PaginationDto } from '@common/dto/pagination.dto';
import { PaginatedResponseDto } from '@common/dto/paginated-response.dto';

@ApiTags('Store')
@Controller({
  path: 'store',
  version: '1',
})
export class StoreController {
  constructor(
    private readonly storeService: StoreService,
    private readonly logger: LoggerService,
    @InjectMapper() private readonly classMapper: Mapper,
  ) {
    this.logger.setContext(StoreService.name);
  }

  @ApiOperation({ summary: 'Create a new store' })
  @ApiBody({ type: CreateStoreDto })
  @Post('new')
  async newStore(
    @Body() createStoreDto: CreateStoreDto,
    @CurrentUser() user: User,
  ) {
    return CoreUtils.handleRequest(async () => {
      const store: Store = await this.classMapper.mapAsync(
        createStoreDto,
        CreateStoreDto,
        Store,
      );
      store.createdBy = `${user.firstName} ${user.lastName}`;

      await this.storeService.create(store);
      return {
        message: 'Store created successfully',
        data: null,
      };
    });
  }

  @ApiOperation({ summary: 'Update an existing store' })
  @ApiBody({ type: StoreDto })
  @Patch('update/:id')
  async updateStore(
    @Param('id') id: number,
    @Body() updateStoreDto: StoreDto,
    @CurrentUser() user: User,
  ) {
    return CoreUtils.handleRequest(async () => {
      const store = await this.storeService.findByPk(id);
      if (!store) {
        throw new NotFoundException('Store not found');
      }

      await this.classMapper.mutateAsync(
        updateStoreDto,
        store,
        StoreDto,
        Store,
      );
      store.updatedBy = `${user.firstName} ${user.lastName}`;

      await this.storeService.update(store);
      return {
        message: 'Store updated successfully',
        data: null,
      };
    });
  }

  @ApiOperation({ summary: 'Retrieve a store' })
  @Get('detail/:id')
  async retrieveAStore(@Param('id') id: number) {
    return CoreUtils.handleRequest(async () => {
      const data = await this.storeService.findByPk(id);
      return {
        message: 'Store retrieved successfully',
        data,
      };
    });
  }

  @ApiOperation({ summary: 'Mark store(s) as active' })
  @ApiBody({ type: ActivateStoreDto })
  @Patch('activate')
  async activate(@Body() body: ActivateStoreDto) {
    return CoreUtils.handleRequest(async () => {
      await this.storeService.activate(body.ids);
      return {
        message: `Store${
          body.ids.length > 1 ? 's' : ''
        } activated successfully`,
        data: null,
      };
    });
  }

  @ApiOperation({ summary: 'Mark store(s) as inactive' })
  @ApiBody({ type: DeactivateStoreDto })
  @Patch('deactivate')
  async deactivate(@Body() body: DeactivateStoreDto) {
    return CoreUtils.handleRequest(async () => {
      await this.storeService.deactivate(body.ids);
      return {
        message: `Store${
          body.ids.length > 1 ? 's' : ''
        } deactivated successfully`,
        data: null,
      };
    });
  }

  @Get()
  @ApiOperation({ summary: 'Get paginated list of stores' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Successful response with paginated stores',
    type: PaginatedResponseDto,
  })
  @ApiQuery({ type: PaginationDto })
  async getPaginatedStores(
    @Query() paginationDto: PaginationDto,
    @CurrentRoute() route: string,
  ) {
    return CoreUtils.handleRequest(async () => {
      const { page, limit, search, filter } = paginationDto;
      const data = await this.storeService.getPaginatedStores(
        {
          page,
          limit,
          search,
          filter,
        },
        route,
      );
      return {
        message: 'Stores retrieved successfully',
        data,
      };
    });
  }

  // server-side search

  // 1) Without pagination
  @ApiOperation({ summary: 'Search for a store' })
  @Get('/search')
  async searchStore(@Query('q') query: string) {
    const data = await this.storeService.search(query);
    return {
      message: 'Stores retrieved successfully',
      data,
    };
  }

  // 2) With pagination
  @ApiOperation({ summary: 'Paginated search for a store' })
  @Get('/paginate-search')
  async paginateSearchStore(
    @Query('q') query: string,
    @Query('page') page: number,
    @Query('limit') limit: number,
  ) {
    const data = this.storeService.paginatedSearch(query, page, limit);
    return {
      message: 'Stores retrieved successfully',
      data,
    };
  }
}
