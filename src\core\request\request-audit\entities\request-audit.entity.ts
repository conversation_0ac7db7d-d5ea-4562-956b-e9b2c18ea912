import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToMany, OneToOne } from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { AbstractEntity } from '@common/entities/base.entity';
import { ItemAudit } from '@core/store/item-audit/entities/item-audit.entity';
import { Request } from '@core/request/entities/request.entity';

@Entity({ name: 'request_audit' })
export class RequestAudit extends AbstractEntity {
  @AutoMap()
  @Column({ name: 'assigned', default: false, type: 'boolean' })
  assigned: boolean;

  @AutoMap()
  @Column({ name: 'assigner', type: 'varchar', nullable: true })
  assigner: string;

  @AutoMap()
  @Column({ name: 'assignee', type: 'bigint', nullable: true })
  assignee: number;

  @AutoMap()
  @Column({ name: 'date_assigned', type: 'timestamp', nullable: true })
  dateAssigned: Date;

  @AutoMap()
  @Column({ name: 'completed', default: false })
  completed: boolean;

  @AutoMap()
  @Column({ name: 'completed_date', nullable: true })
  completedDate: Date;

  @AutoMap()
  @Column({ name: 'completed_by', nullable: true })
  completedBy: string;

  @AutoMap()
  @Column({ name: 'collected', default: false })
  collected: boolean;

  @AutoMap()
  @Column({ name: 'collected_date', nullable: true })
  collectedDate: Date;

  @AutoMap()
  @Column({ name: 'collected_by', nullable: true })
  collectedBy: string;

  @AutoMap()
  @OneToMany(() => ItemAudit, (itemAudit) => itemAudit.requestAudit, {
    cascade: true,
    eager: true,
    orphanedRowAction: 'delete',
  })
  items: Array<ItemAudit>;

  @AutoMap()
  @OneToOne(() => Request)
  @JoinColumn({ name: 'request_id', referencedColumnName: 'id' })
  request: Request;
}
