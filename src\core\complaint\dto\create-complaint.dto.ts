import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsString } from 'class-validator';

export class CreateComplaintDto {
  @AutoMap()
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  complainerName: string;

  @AutoMap()
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  complainerPhone: string;

  @AutoMap()
  @ApiProperty()
  @IsEmail()
  complainerEmail: string;

  @AutoMap()
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  complaintSubject: string;

  @AutoMap()
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  complaintDescription: string;
}
