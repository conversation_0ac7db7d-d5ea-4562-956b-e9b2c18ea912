import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { EntityStatus } from '@common/entities/base.entity';
import { DatabaseAction } from '@common/enums/dbaction.enum';
import { Role } from '@common/enums/roles.enum';
import { PaginationQueryParams } from '@common/utils/pagination_query_params.type';
import { CoreUtils } from '@common/utils/core.utils';
import { MailService } from '@core/notification/mail-service/mail.service';
import { DepartmentService } from '@core/security/department/department.service';
import { DepartmentValidatorService } from '@core/security/department/department.validator.service';
import { User } from '@core/security/user/entities/user.entity';
import { UserService } from '@core/security/user/user.service';
import { ItemUnit } from '@core/store/item-unit/entities/item-unit.entity';
import { StoreValidatorService } from '@core/store/store.validator.service';
import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import * as _ from 'lodash';
import {
  IPaginationOptions,
  paginate,
  Pagination,
} from 'nestjs-typeorm-paginate';
import { EntityManager, ILike, Repository } from 'typeorm';
import { UpdateItemUnitDto } from '../item-unit/dto/update-item-unit.dto';
import { ItemUnitService } from '../item-unit/item-unit.service';
import { UpdateItemDto } from './dto/update-item.dto';
import { Item } from './entities/item.entity';
import { ItemValidatorService } from './item.validator.service';
import { Condition } from '@common/enums/condition.enum';

@Injectable()
export class ItemService {
  private readonly backOfficeUrl: string;

  constructor(
    @InjectRepository(Item) private readonly itemRepository: Repository<Item>,
    private readonly itemValidator: ItemValidatorService,
    private readonly storeValidator: StoreValidatorService,
    private readonly departmentValidator: DepartmentValidatorService,
    private readonly itemUnitService: ItemUnitService,
    @InjectMapper() private readonly classMapper: Mapper,
    private readonly departmentService: DepartmentService,
    private readonly userService: UserService,
    private readonly mailService: MailService,
    private readonly configService: ConfigService,
    @InjectEntityManager() private readonly entityManager: EntityManager,
  ) {
    this.backOfficeUrl = this.configService.get<string>('backOfficeUrl');
  }

  private async validateDependencies(item: Item): Promise<void> {
    if (
      !(await this.departmentValidator.isDepartmentExists(item.department.id))
    ) {
      throw new NotFoundException(
        `Department with id ${item.department.id} does not exist`,
      );
    }
  }

  private generateItemDetailsUrl(itemId: number) {
    return `${this.backOfficeUrl}item/${itemId}`;
  }

  async create(item: Item, user: User) {
    const department = await this.departmentService.findByPk(
      item.department.id,
    );
    if (!department) {
      throw new NotFoundException('Department not found');
    }

    item.createdBy = `${user.firstName} ${user.lastName}`;

    // At the initial stage, the available quantity is the same as the quantity
    item.availableQuantity = item.actualQuantity;
    item.itemUnits = [];

    // Generate serial number
    const departmentItems = await this.itemRepository.findBy({
      department: { id: department.id },
    });

    // Based on total number of item units, create new item records in the database new serial number for each item.

    let count = departmentItems.reduce((acc, cur) => {
      acc += cur.itemUnits.length;
      return acc;
    }, 0);

    for (let i = 0; i < item.actualQuantity; i++) {
      const itemUnit = new ItemUnit();
      itemUnit.serialNumber = CoreUtils.generateSerialNumber(
        department.name,
        item.name,
        'EGFM',
        count,
      );
      itemUnit.item = item;
      itemUnit.condition = Condition.NOT_SPECIFIED;
      item.itemUnits.push(itemUnit);
      count++;
    }

    await this.itemValidator.validate(item, DatabaseAction.CREATE);
    const createdItem = await this.itemRepository.save(item);
    const itemUrl = this.generateItemDetailsUrl(createdItem.id);

    // Send email to all super admins if it's an HOD that created the item
    if (user.role.name === Role.HOD) {
      const superAdmins = await this.userService.fetchAllUsersByRole(
        Role.SUPER_ADMIN,
      );
      await Promise.all(
        superAdmins.map((admin) =>
          this.mailService.sendSmtpMail(
            admin.email,
            'Item Creation',
            `An item was created by ${user.firstName} ${user.lastName}. Kindly click the link to view item details: ${itemUrl}. \nRemain blessed.`,
          ),
        ),
      );
    }
    return createdItem;
  }

  async createMany(items: Item[], user: User) {
    await this.entityManager.transaction(async (manager) => {
      const itemsByDepartment = _.groupBy(items, 'departmentId');

      for (const [departmentId, departmentItems] of Object.entries(
        itemsByDepartment,
      )) {
        const department = await this.departmentService.findByPk(
          Number(departmentId),
        );
        if (!department) {
          throw new NotFoundException(
            `Department with id ${departmentId} not found`,
          );
        }

        const existingDepartmentItems = await manager.findBy(Item, {
          department: { id: department.id },
        });

        let count = existingDepartmentItems.reduce((acc, cur) => {
          acc += cur.itemUnits.length;
          return acc;
        }, 0);

        // Process each item in this department
        for (const item of departmentItems as Item[]) {
          item.createdBy = `${user.firstName} ${user.lastName}`;
          item.availableQuantity = item.actualQuantity;
          item.itemUnits = [];

          for (let i = 0; i < item.actualQuantity; i++) {
            const itemUnit = new ItemUnit();
            itemUnit.serialNumber = CoreUtils.generateSerialNumber(
              department.name,
              item.name,
              'EGFM',
              count,
            );
            itemUnit.item = item;
            item.itemUnits.push(itemUnit);
            count++;
          }

          // Validate each item
          await this.itemValidator.validate(item, DatabaseAction.CREATE);
          await this.validateDependencies(item);
        }
      }

      await manager.save(items);
    });
  }

  async update(item: Item): Promise<Item | undefined> {
    await this.itemValidator.validate(item, DatabaseAction.UPDATE);
    await this.validateDependencies(item);
    return this.itemRepository.save(item);
  }

  async updateItem(id: number, updateItemDto: UpdateItemDto, user: User) {
    const item = await this.findByPk(id);
    if (!item) {
      throw new NotFoundException('Item not found');
    }

    // Validate item units if they exist in the update DTO
    if ('itemUnits' in updateItemDto) {
      for (const itemUnitDto of updateItemDto.itemUnits) {
        const existingItemUnit = await this.itemUnitService.findByPk(
          itemUnitDto.id,
        );
        if (!existingItemUnit) {
          throw new NotFoundException(
            `Item unit with ${itemUnitDto.id} not found `,
          );
        }

        if (
          item.itemUnits.findIndex(
            (unit) => unit.id === existingItemUnit.id,
          ) === -1
        ) {
          throw new ConflictException(
            `Item unit with id ${itemUnitDto.id} does not belong to item with id ${id}`,
          );
        }

        if ('storeId' in itemUnitDto && itemUnitDto.storeId) {
          const storeExists = await this.storeValidator.isStoreExists(
            itemUnitDto.storeId,
          );
          if (!storeExists) {
            throw new NotFoundException(
              `Store with id ${itemUnitDto.storeId} does not exist`,
            );
          }
        }
      }

      // Update item units using mapper
      for (const itemUnitDto of updateItemDto.itemUnits) {
        const existingItemUnit = item.itemUnits.find(
          (unit) => unit.id === itemUnitDto.id,
        );
        if (existingItemUnit) {
          await this.classMapper.mutateAsync(
            itemUnitDto,
            existingItemUnit,
            UpdateItemUnitDto,
            ItemUnit,
          );
        }
      }
    }

    // await this.classMapper.mutateAsync(
    //   updateItemDto,
    //   item,
    //   UpdateItemDto,
    //   Item,
    // );

    const { itemUnits, ...itemUpdateData } = updateItemDto;
    Object.assign(item, itemUpdateData);

    item.updatedBy = `${user.firstName} ${user.lastName}`;

    await this.update(item);
  }
  async getItem(id: number) {
    const item = await this.findByPk(id);
    if (!item) {
      throw new NotFoundException('Item not found');
    }

    const existingDepartment = await this.departmentService.findByPk(
      item.department.id,
    );

    if (!existingDepartment) {
      throw new NotFoundException('No department is associated with this item');
    }

    return { item, existingDepartment };
  }

  async getItemsAssociatedWithDepartment(departmentId: number) {
    const department = await this.departmentService.findByPk(departmentId);
    if (!department) {
      throw new NotFoundException('Department not found');
    }

    return await this.fetchItemsByDepartment(departmentId);
  }

  async getPaginatedItemsAssociatedWithDepartment(
    departmentId: number,
    params: PaginationQueryParams,
    route: string,
  ) {
    const department = await this.departmentService.findByPk(departmentId);
    if (!department) {
      throw new NotFoundException('Department not found');
    }

    const { page, limit, search, filter } = params;
    const where = { departmentId };

    if (search) {
      where['name'] = ILike(`%${search}%`);
    }

    if (filter) {
      where['status'] = filter;
    }

    const options = {
      page,
      limit: limit > 100 ? 100 : limit, // limit the pagination to 100
      route,
    };

    const pagination = await this.paginate(options, where);

    return {
      items: pagination.items,
      meta: pagination.meta,
      links: pagination.links,
    };
  }

  async findAll(): Promise<Array<Item>> {
    return this.itemRepository.find();
  }

  async findByPk(id: number): Promise<Item> {
    return this.itemRepository.findOneBy({ id });
  }

  async findFirstBy(where: any): Promise<Item> {
    return this.itemRepository.findOneBy(where);
  }

  async delete(id: number): Promise<void> {
    await this.itemRepository.delete(id);
  }

  async activate(ids: Array<number>): Promise<void> {
    ids.map(async (id) => {
      const item: Item = await this.findByPk(id);
      item.status = EntityStatus.ACTIVE;
      await this.update(item);
    });
  }

  async deactivate(ids: Array<number>): Promise<void> {
    ids.map(async (id) => {
      const item: Item = await this.findByPk(id);
      item.status = EntityStatus.INACTIVE;
      await this.update(item);
    });
  }

  async getPaginatedItems(params: PaginationQueryParams, route: string) {
    const { page, limit, search, filter } = params;
    const where = {};
    if (search) {
      where['name'] = ILike(`%${search}%`);
    }
    if (filter) {
      where['createdDate'] = filter;
      where['status'] = filter;
    }

    const options = {
      page,
      limit: limit > 100 ? 100 : limit, // limit the pagination to 100
      route,
    };

    const pagination = await this.paginate(options, where);

    const departmentIds = pagination.items.map((item) => item.department.id);
    const departments = await this.departmentService.fetchDepartmentsByPks(
      departmentIds,
    );

    const departmentMap = _.keyBy(departments, 'id');

    return {
      items: pagination.items,
      meta: pagination.meta,
      links: pagination.links,
      departmentMap,
    };
  }

  async paginate(
    options: IPaginationOptions,
    where?: any,
  ): Promise<Pagination<Item>> {
    if (where && Object.keys(where).length > 0) {
      return paginate<Item>(this.itemRepository, options, {
        where,
        order: {
          createdAt: 'DESC',
        },
      });
    }
    return paginate<Item>(this.itemRepository, options);
  }

  // server side search without pagination
  async search(query: string): Promise<Array<Item>> {
    return await this.itemRepository.find({
      where: [{ name: ILike(`%${query}%`) }],
    });
  }

  // server side search with pagination
  async paginatedSearch(
    query: string,
    page = 1,
    limit = 10,
  ): Promise<Array<Item>> {
    const [results] = await this.itemRepository.findAndCount({
      where: [{ name: ILike(`%${query}%`) }],
      skip: (page - 1) * limit,
      take: limit,
    });
    return results;
  }

  async fetchItemsByDepartment(departmentId: number): Promise<Array<Item>> {
    return Promise.resolve(
      await this.itemRepository.findBy({
        department: { id: departmentId },
      }),
    );
  }

  async fetchItemsByIds(ids: number[]): Promise<Array<Item>> {
    // Use a set to avoid duplicate IDs and improve efficiency
    const uniqueIds = Array.from(new Set(ids));

    // Ensure all items are unique and exist
    return await Promise.all(
      uniqueIds.map(async (id) => {
        const item = await this.findByPk(id);
        if (!item) {
          throw new NotFoundException(`Item with id ${id} does not exist`);
        }
        return item;
      }),
    );
  }

  async deleteItems(ids: Array<number>): Promise<void> {
    await Promise.all(
      ids.map(async (id) => {
        await this.delete(id);
      }),
    );
  }

  async deleteAnItem(id: number): Promise<void> {
    const item = await this.findByPk(id);
    if (!item) {
      throw new NotFoundException('Item not found');
    }
    await this.delete(id);
  }

  /**
   * Checks if an item is available based on its ID.
   * This method checks if the item has a non-zero available quantity
   * @param itemId
   */
  async isItemAvailable(itemId: number): Promise<boolean> {
    return Promise.resolve(
      this.itemRepository
        .count({
          where: {
            id: itemId,
            availableQuantity: 0,
            status: EntityStatus.ACTIVE,
          },
        })
        .then((count) => count === 0),
    );
  }
}
