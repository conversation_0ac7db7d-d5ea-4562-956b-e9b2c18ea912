import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { CheckAbilities } from '@common/decorators/abilities.decorator';
import { CurrentRoute } from '@common/decorators/current-route.decorator';
import { CurrentUser } from '@common/decorators/current-user.decorator';
import { EntityStatus } from '@common/entities/base.entity';
import { Role } from '@common/enums/roles.enum';
import { UserActions } from '@common/enums/user-actions.enum';
import { CoreUtils } from '@common/utils/core.utils';
import { DepartmentService } from '@core/security/department/department.service';
import {
  Body,
  Controller,
  DefaultValuePipe,
  Delete,
  Get,
  HttpStatus,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Req,
  Res,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBody,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { Pagination } from 'nestjs-typeorm-paginate';
import { AbilityGuard } from '../authorization/ability/ability.guard';
import { RoleService } from '../role/role.service';
import { CreateUserDto } from './dto/create-user.dto';
import { InitiateUserDto } from './dto/initiate-user.dto';
import { UserDetailDto } from './dto/user-detail.dto';
import { UserDto } from './dto/user.dto';
import { User } from './entities/user.entity';
import { UserService } from './user.service';

@UseGuards(AbilityGuard)
@ApiTags('User')
@Controller({
  path: 'user',
  version: '1',
})
export class UserController {
  constructor(
    private readonly userService: UserService,
    @InjectMapper() private readonly classMapper: Mapper,
    private readonly departmentService: DepartmentService,
    private readonly roleService: RoleService,
  ) {}

  @ApiOperation({ summary: 'Create a new user' })
  @Post('create')
  @ApiBody({ type: CreateUserDto })
  async create(
    @Body() userDto: CreateUserDto,
    @Res() response: any,
    @Req() request: any,
  ): Promise<any> {
    try {
      // Helper function to handle responses
      const handleResponse = (status: HttpStatus, message: string) =>
        response.status(status).json({
          statusCode: status,
          message,
        });

      // Fetch role and department concurrently
      const [existingRole, existingDept] = await Promise.all([
        this.roleService.findByPk(userDto.roleId),
        this.departmentService.findByPk(userDto.departmentId),
      ]);

      if (!existingRole) {
        return handleResponse(HttpStatus.NOT_FOUND, 'Role does not exist');
      }
      if (!existingDept) {
        return handleResponse(
          HttpStatus.NOT_FOUND,
          'Department does not exist',
        );
      }

      // Map DTO to User entity
      const user: User = await this.classMapper.mapAsync(
        userDto,
        CreateUserDto,
        User,
      );

      // Assign role and department
      user.department = existingDept;
      user.role = existingRole;
      user.createdBy = `${request?.user?.firstName || 'System'} ${
        request?.user?.lastName || ''
      }`;

      // Create user
      await this.userService.create(user);

      return handleResponse(HttpStatus.CREATED, 'User created successfully');
    } catch (err) {
      console.error('Error creating user:', err.message); // Optional: Log the error
      return CoreUtils.genericResponse(
        response,
        HttpStatus.INTERNAL_SERVER_ERROR,
        err.message,
      );
    }
  }

  @ApiOperation({ summary: 'Update a user' })
  @Patch('/update/:userId')
  @ApiBody({ type: UserDto })
  async update(
    @Param('userId') userId: number,
    @Body() userDto: UserDto,
    @Res() response: any,
    @Req() request: any,
  ): Promise<any> {
    const user = await this.userService.findByPk(userId);
    if (!user) {
      return response
        .status(HttpStatus.NOT_FOUND)
        .json({ statusCode: HttpStatus.NOT_FOUND, message: 'User not found' });
    } else if (user.status === EntityStatus.INACTIVE) {
      return CoreUtils.genericResponse(
        response,
        HttpStatus.BAD_REQUEST,
        'User is inactive',
      );
    } else {
      await this.classMapper.mutateAsync(userDto, user, UserDto, User);
      user.updatedBy =
        `${request?.user?.firstName} ${request?.user?.lastName}` || 'System';
      await this.userService.update(user);
      return response.status(HttpStatus.OK).json({
        statusCode: HttpStatus.OK,
        message: 'User updated successfully',
      });
    }
  }

  @Get()
  @ApiOperation({ summary: 'Get paginated list of users' })
  @ApiQuery({ name: 'page', required: true, type: Number, example: 1 })
  @ApiQuery({ name: 'limit', required: true, type: Number, example: 10 })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'filter', required: false, type: String })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Successful response with paginated users',
    type: Pagination,
  })
  async getPaginatedUsers(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit,
    @Query('search') search: string,
    @Query('filter') filter: string,
    @CurrentRoute() route: string,
  ) {
    return CoreUtils.handleRequest(async () => {
      const data = await this.userService.getPaginatedUsers(
        {
          page,
          limit,
          search,
          filter,
        },
        route,
      );
      return { message: 'Users retrieved successfully', data };
    });
  }

  // server-side search

  // 1) Without pagination
  @ApiOperation({ summary: 'Search for users' })
  @Get('/search')
  async searchUser(@Query('q') query: string) {
    return CoreUtils.handleRequest(async () => {
      const data = await this.userService.search(query);
      return { message: 'Users retrieved successfully', data };
    });
  }

  // 2) With pagination
  @ApiOperation({ summary: 'Paginated search for users' })
  @Get('/paginate-search')
  async paginateSearchStore(
    @Query('q') query: string,
    @Query('page') page: number,
    @Query('limit') limit: number,
  ) {
    return CoreUtils.handleRequest(async () => {
      const data = await this.userService.paginatedSearch(query, page, limit);
      return { message: 'Users retrieved successfully', data };
    });
  }

  @ApiOperation({ summary: 'Make user(s) active' })
  @Post('activate')
  async activate(@Res() response: any, @Body() ids: Array<number>) {
    try {
      await this.userService.activate(ids);
      response.status(HttpStatus.OK).json({ storeIds: ids });
    } catch (err) {
      return response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: `Internal server error: ${err.message}`,
      });
    }
  }

  @ApiOperation({ summary: 'Make user(s) inactive' })
  @Post('deactivate')
  async deactivate(@Res() response: any, @Body() ids: Array<number>) {
    try {
      await this.userService.deactivate(ids);
      response.status(HttpStatus.OK).json({ storeIds: ids });
    } catch (err) {
      return response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: `Internal server error: ${err.message}`,
      });
    }
  }

  @ApiOperation({ summary: 'Initiate a new user' })
  @Post('initiate')
  @ApiBody({ type: InitiateUserDto })
  async initiateNewUser(
    @Body() initiateUserDto: InitiateUserDto,
    @CurrentUser() currentUser: User,
  ) {
    return CoreUtils.handleRequest(async () => {
      await this.userService.initiateUser(initiateUserDto, currentUser);
      return {
        message: 'User initiated successfully',
        data: null,
      };
    });
  }

  @Get('all')
  @ApiOperation({ summary: 'Fetch all users by a role' })
  @ApiBody({ type: String })
  async fetchAllUsersByRole(@Res() response: any, @Body() role: Role) {
    try {
      const users = await this.userService.fetchAllUsersByRole(role);
      return CoreUtils.genericResponse(
        response,
        HttpStatus.OK,
        'Users fetched successfully',
        await this.classMapper.mapArrayAsync(users, User, UserDto),
      );
    } catch (err) {
      return CoreUtils.genericResponse(
        response,
        HttpStatus.INTERNAL_SERVER_ERROR,
        err.message,
      );
    }
  }

  @Get('details/:userEmail')
  @ApiOperation({ summary: 'Fetch user by email' })
  @ApiOkResponse({
    description: 'User fetched successfully',
    type: UserDto,
  })
  async fetchUserByEmail(@Res() response: any, @Param('userEmail') id: string) {
    try {
      const user = await this.userService.findByEmail(id);
      if (!user) {
        return CoreUtils.genericResponse(
          response,
          HttpStatus.NOT_FOUND,
          'User not found',
        );
      } else {
        return CoreUtils.genericResponse(
          response,
          HttpStatus.OK,
          'User fetched successfully',
          await this.classMapper.mapAsync(user, User, UserDto),
        );
      }
    } catch (err) {
      return CoreUtils.genericResponse(
        response,
        HttpStatus.INTERNAL_SERVER_ERROR,
        `Internal server error: ${err.message}`,
      );
    }
  }

  @Get('details/:userId')
  @ApiOperation({ summary: 'Fetch user by ID' })
  @ApiOkResponse({
    description: 'User fetched successfully',
    type: UserDto,
  })
  async fetchUserById(@Res() response: any, @Param('userId') id: number) {
    try {
      const user = await this.userService.findByPk(id);
      if (!user) {
        return CoreUtils.genericResponse(
          response,
          HttpStatus.NOT_FOUND,
          `User with ID ${id} does not exist in the system`,
        );
      } else {
        return CoreUtils.genericResponse(
          response,
          HttpStatus.OK,
          'User fetched successfully',
          await this.classMapper.mapAsync(user, User, UserDetailDto),
        );
      }
    } catch (err) {
      return CoreUtils.genericResponse(
        response,
        HttpStatus.INTERNAL_SERVER_ERROR,
        `${err.message}`,
      );
    }
  }

  @ApiOperation({ summary: 'Delete user.' })
  @CheckAbilities({ action: UserActions.DELETE, subject: User })
  @Delete(':id')
  async delete(@Param('id') id: number, @Res() response: any) {
    try {
      await this.userService.delete(id);
      return response.status(HttpStatus.OK).json({
        statusCode: HttpStatus.OK,
        message: 'User deleted successfully',
      });
    } catch (err) {
      return CoreUtils.genericResponse(
        response,
        HttpStatus.INTERNAL_SERVER_ERROR,
        err.message,
      );
    }
  }

  @Get('role/:roleId')
  @ApiOperation({ summary: 'Fetch all users by role ID' })
  @ApiOkResponse({
    description: 'Users with the specified role fetched successfully',
    type: [UserDto],
  })
  async fetchUsersByRoleId(
    @Param('roleId', ParseIntPipe) roleId: number,
    @Res() response: any,
  ) {
    try {
      const users = await this.userService.fetchUsersByRoleId(roleId);

      if (!users || users.length === 0) {
        return CoreUtils.genericResponse(
          response,
          HttpStatus.NOT_FOUND,
          `No users found with the role ID ${roleId}`,
        );
      }

      return CoreUtils.genericResponse(
        response,
        HttpStatus.OK,
        `Users with the role ID ${roleId} fetched successfully`,
        await this.classMapper.mapArrayAsync(users, User, UserDto),
      );
    } catch (err) {
      return CoreUtils.genericResponse(
        response,
        HttpStatus.INTERNAL_SERVER_ERROR,
        `Internal server error: ${err.message}`,
      );
    }
  }
}
