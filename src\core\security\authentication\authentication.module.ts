import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule, JwtService } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { CommonModule } from '../../../common/common.module';
import { LoggerModule } from '../../../common/logger/logger.module';
import { NotificationModule } from '../../notification/notification.module';
import { PermissionModule } from '../permission/permission.module';
import { RoleModule } from '../role/role.module';
import { UserModule } from '../user/user.module';
import { AuthenticationController } from './authentication.controller';
import { AuthenticationService } from './authentication.service';
import { AccessTokenStrategy } from './strategy/access-token.strategy';
import { RefreshTokenStrategy } from './strategy/refresh-token.strategy';

@Module({
  controllers: [AuthenticationController],
  exports: [AuthenticationService],
  imports: [
    RoleModule,
    UserModule,
    PermissionModule,
    LoggerModule,
    CommonModule,
    NotificationModule,
    PassportModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('keys.secret'),
        expiresIn: 60 * 15,
      }),
    }),
  ],
  providers: [
    AuthenticationService,
    AccessTokenStrategy,
    // LoggerService,
    JwtService,
    RefreshTokenStrategy,
  ],
})
export class AuthenticationModule {}
