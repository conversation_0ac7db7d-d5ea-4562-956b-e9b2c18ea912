import {
  Body,
  Controller,
  DefaultValuePipe,
  Get,
  HttpStatus,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Req,
  Res,
} from '@nestjs/common';
import { PermissionService } from './permission.service';
import { PermissionDto } from './dto/permission.dto';
import {
  ApiBody,
  ApiExcludeEndpoint,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { InjectMapper } from '@automapper/nestjs';
import { Mapper } from '@automapper/core';
import { ILike } from 'typeorm';
import { Permission } from './entities/permission.entity';
import { Pagination } from 'nestjs-typeorm-paginate';
import { CreatePermissionDto } from './dto/create-permission.dto';

@ApiTags('Permission')
@Controller({
  path: 'permission',
  version: '1',
})
export class PermissionController {
  constructor(
    private readonly permissionService: PermissionService,
    @InjectMapper() private readonly classMapper: Mapper,
  ) {}

  @ApiOperation({ summary: 'Get all permissions' })
  @Get()
  async index(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page = 1,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit = 10,
    @Query('search') search: string,
    @Query('filter') filter: string,
    @Req() request: any,
    @Res() response: any,
  ) {
    limit = limit > 100 ? 100 : limit; // limit the pagination to 100
    const where = {};
    if (search) {
      where['name'] = ILike(`%${search}%`);
    }
    if (filter) {
      where['status'] = filter;
    }
    const pagination = await this.permissionService.paginate(
      {
        page,
        limit,
        route: `/api/permission`,
      },
      where,
    );
    const dtoList = await this.classMapper.mapArrayAsync(
      pagination.items,
      Permission,
      PermissionDto,
    );

    response
      .status(HttpStatus.OK)
      .json(new Pagination(dtoList, pagination.meta, pagination.links));
  }

  @ApiExcludeEndpoint()
  @ApiOperation({ summary: 'Get available permissions' })
  @Get('available')
  async availablePermissions(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page = 1,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit = 10,
    @Query('search') search: string,
    @Query('role_id') roleId: number,
    @Req() request: any,
    @Res() response: any,
  ) {
    limit = limit > 100 ? 100 : limit; // limit the pagination to 100
    const pagination = await this.permissionService.availablePermissions(
      roleId,
      page,
      limit,
      search,
    );
    const dtoList = pagination.items.map((result) => {
      const dto: PermissionDto = new PermissionDto();
      dto.id = result['permission_id'];
      dto.name = result['permission_name'];
      dto.description = result['permission_description'];
      dto.status = result['permission_status'];
      dto.createdAt = result['permission_created_at'];
      dto.updatedAt = result['permission_updated_at'];
      return dto;
    });
    response
      .status(HttpStatus.OK)
      .json(new Pagination(dtoList, pagination.meta, pagination.links));
  }

  @ApiExcludeEndpoint()
  @ApiOperation({ summary: 'Get assigned permissions' })
  @Get('assigned')
  async assignedPermissions(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page = 1,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit = 10,
    @Query('search') search: string,
    @Query('role_id') roleId: number,
    @Req() request: any,
    @Res() response: any,
  ) {
    limit = limit > 100 ? 100 : limit; // limit the pagination to 100
    const pagination = await this.permissionService.assignedPermissions(
      roleId,
      page,
      limit,
      search,
    );
    const dtoList = pagination.items.map((result) => {
      const dto: PermissionDto = new PermissionDto();
      dto.id = result['permission_id'];
      dto.name = result['permission_name'];
      dto.description = result['permission_description'];
      dto.status = result['permission_status'];
      dto.createdAt = result['permission_created_at'];
      dto.updatedAt = result['permission_updated_at'];
      return dto;
    });
    response
      .status(HttpStatus.OK)
      .json(new Pagination(dtoList, pagination.meta, pagination.links));
  }

  @Post('new')
  @ApiOperation({ summary: 'Create a new permission' })
  @ApiBody({ type: CreatePermissionDto })
  async create(
    @Body() createPermissionDto: CreatePermissionDto,
    @Res() response: any,
    @Req() request: any,
  ) {
    try {
      const permission = await this.classMapper.mapAsync(
        createPermissionDto,
        CreatePermissionDto,
        Permission,
      );
      permission.createdBy =
        `${request?.user?.firstName} ${request?.user?.lastName}` ?? 'System';
      await this.permissionService.create(permission);
      return response.status(HttpStatus.CREATED).json({
        statusCode: HttpStatus.CREATED,
        message: 'Permission created successfully',
      });
    } catch (err) {
      return response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: `Internal server error: ${err.message}`,
      });
    }
  }

  @ApiOperation({ summary: 'Get all permissions' })
  @Get('all')
  async findAll(@Res() response: any) {
    try {
      const allPermissions = await this.permissionService.findAll();
      return response.status(HttpStatus.OK).json({
        statusCode: HttpStatus.OK,
        message: 'Permissions found successfully',
        data: await this.classMapper.mapArrayAsync(
          allPermissions,
          Permission,
          PermissionDto,
        ),
      });
    } catch (err) {
      return response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: `Internal server error: ${err.message}`,
      });
    }
  }

  @ApiOperation({ summary: 'Get a permission by ID' })
  @Get(':id')
  async findOne(@Param('id') id: number, @Res() response: any) {
    try {
      const existingPermission = await this.permissionService.findByPk(id);
      return response.status(HttpStatus.OK).json({
        statusCode: HttpStatus.OK,
        message: 'Permission found successfully',
        data: await this.classMapper.mapAsync(
          existingPermission,
          Permission,
          PermissionDto,
        ),
      });
    } catch (err) {
      return response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: `Internal server error: ${err.message}`,
      });
    }
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a permission by ID' })
  @ApiBody({ type: PermissionDto })
  async update(
    @Param('id') id: number,
    @Body() updatePermissionDto: PermissionDto,
    @Res() response: any,
    @Req() request: any,
  ) {
    try {
      const permission = await this.permissionService.findByPk(id);
      await this.classMapper.mutateAsync(
        updatePermissionDto,
        permission,
        PermissionDto,
        Permission,
      );

      permission.updatedBy =
        `${request?.user?.firstName} ${request?.user?.lastName}` ?? 'System';
      await this.permissionService.update(permission);
      return response.status(HttpStatus.OK).json({
        statusCode: HttpStatus.OK,
        message: 'Permission updated successfully',
      });
    } catch (err) {
      return response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: `Internal server error: ${err.message}`,
      });
    }
  }

  @ApiOperation({ summary: 'Delete a permission by ID' })
  @Post('activate')
  async activate(
    @Req() request: any,
    @Res() response: any,
    @Body() ids: Array<number>,
  ) {
    try {
      await this.permissionService.activate(ids);
      return response.status(HttpStatus.OK).json({
        statusCode: HttpStatus.OK,
        message: 'Permissions activated successfully',
      });
    } catch (err) {
      return response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: `Internal server error: ${err.message}`,
      });
    }
  }

  @ApiOperation({ summary: 'Delete a permission by ID' })
  @Post('deactivate')
  async deactivate(
    @Req() request: any,
    @Res() response: any,
    @Body() ids: Array<number>,
  ) {
    try {
      await this.permissionService.deactivate(ids);
      return response.status(HttpStatus.OK).json({
        statusCode: HttpStatus.OK,
        message: 'Permissions deactivated successfully',
      });
    } catch (err) {
      return response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: `Internal server error: ${err.message}`,
      });
    }
  }
}
