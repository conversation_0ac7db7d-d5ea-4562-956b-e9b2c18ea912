import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { EntityStatus } from '@common/entities/base.entity';
import { DatabaseAction } from '@common/enums/dbaction.enum';
import { PaginationQueryParams } from '@common/utils/pagination_query_params.type';
import { User } from '@core/security/user/entities/user.entity';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  IPaginationOptions,
  paginate,
  Pagination,
} from 'nestjs-typeorm-paginate';
import { ILike, Repository } from 'typeorm';
import { DepartmentValidatorService } from './department.validator.service';
import { CreateDepartmentDto } from './dto/create-department.dto';
import { DepartmentDto } from './dto/department.dto';
import { UpdateDepartmentDto } from './dto/update-department.dto';
import { Department } from './entities/department.entity';

@Injectable()
export class DepartmentService {
  constructor(
    @InjectRepository(Department)
    private readonly departmentRepository: Repository<Department>,
    private readonly departmentValidator: DepartmentValidatorService,
    @InjectMapper() private readonly classMapper: Mapper,
  ) {}

  async createDepartment(createDepartmentDto: CreateDepartmentDto, user: User) {
    const department = await this.classMapper.mapAsync(
      createDepartmentDto,
      CreateDepartmentDto,
      Department,
    );
    department.createdBy = `${user.firstName} ${user.lastName}`;
    await this.departmentValidator.validate(department, DatabaseAction.CREATE);
    return this.departmentRepository.save(department);
  }

  async updateDepartment(
    id: number,
    updateDepartmentDto: UpdateDepartmentDto,
    user: User,
  ) {
    const department = await this.findByPk(id);

    if (!department) {
      throw new NotFoundException('Department not found');
    }

    await this.classMapper.mutateAsync(
      updateDepartmentDto,
      department,
      DepartmentDto,
      Department,
    );
    department.updatedBy = `${user.firstName} ${user.lastName}`;
    await this.update(department);
  }

  async getDepartment(id: number) {
    const department = await this.findByPk(id);
    if (!department) {
      throw new NotFoundException('Department not found');
    }
    return await this.classMapper.mapAsync(
      department,
      Department,
      DepartmentDto,
    );
  }

  async update(department: Department): Promise<Department | undefined> {
    await this.departmentValidator.validate(department, DatabaseAction.UPDATE);
    return this.departmentRepository.save(department);
  }

  async findAll(): Promise<Array<Department>> {
    return this.departmentRepository.find();
  }

  async findByPk(id: number): Promise<Department> {
    return this.departmentRepository.findOneBy({ id });
  }

  async findFirstBy(where: any): Promise<Department> {
    return this.departmentRepository.findOneBy(where);
  }

  async delete(id: number): Promise<void> {
    await this.departmentRepository.delete(id);
  }

  async activate(ids: Array<number>): Promise<void> {
    await Promise.all(
      ids.map(async (id) => {
        const department: Department = await this.findByPk(id);
        department.status = EntityStatus.ACTIVE;
        await this.update(department);
      }),
    );
  }

  async deactivate(ids: Array<number>): Promise<void> {
    await Promise.all(
      ids.map(async (id) => {
        const department: Department = await this.findByPk(id);
        department.status = EntityStatus.INACTIVE;
        await this.update(department);
      }),
    );
  }

  async getPaginatedDepartments(params: PaginationQueryParams, route: string) {
    const { page, limit, search, filter } = params;
    const where = {};
    if (search) {
      where['name'] = ILike(`%${search}%`);
      where['hodName'] = ILike(`%${search}%`);
    }
    if (filter) {
      where['status'] = filter;
    }

    const options = {
      page,
      limit: limit > 100 ? 100 : limit, // limit the pagination to 100
      route,
    };

    const pagination = await this.paginate(options, where);

    const dtoList = await this.classMapper.mapArrayAsync(
      pagination.items,
      Department,
      DepartmentDto,
    );

    return new Pagination(dtoList, pagination.meta, pagination.links);
  }

  async paginate(
    options: IPaginationOptions,
    where?: any,
  ): Promise<Pagination<Department>> {
    if (where && Object.keys(where).length > 0) {
      return paginate<Department>(this.departmentRepository, options, {
        where,
        order: {
          createdAt: 'DESC',
        },
      });
    }
    return paginate<Department>(this.departmentRepository, options);
  }

  // server side search without pagination
  async search(query: string): Promise<Array<Department>> {
    return await this.departmentRepository.find({
      where: [{ name: ILike(`%${query}%`) }],
    });
  }

  // server side search with pagination
  async paginatedSearch(
    query: string,
    page = 1,
    limit = 10,
  ): Promise<Array<Department>> {
    const [results] = await this.departmentRepository.findAndCount({
      where: [{ name: ILike(`%${query}%`) }],
      skip: (page - 1) * limit,
      take: limit,
    });
    return results;
  }

  async fetchAllDepartmentNames() {
    const departments = await this.departmentRepository
      .createQueryBuilder('department')
      .select([
        'CAST(department.id AS INTEGER) as id',
        'department.name',
        'department.hodName',
        'department.hodEmail',
        'department.hodPhone',
        'department.status',
      ])
      .leftJoin('item', 'item', 'item.department_id = department.id')
      .addSelect('CAST(COUNT(item.id) AS INTEGER)', 'itemCount')
      .groupBy('department.id')
      .getRawMany();

    return departments.map((department) => ({
      id: department.id,
      name: department.department_name,
      hodName: department.department_hod_name,
      hodEmail: department.department_hod_email,
      hodPhone: department.department_hod_phone,
      status: department.department_status,
      itemCount: department.itemCount,
    }));
  }

  async fetchDepartmentsByPks(ids: Array<number>): Promise<Array<Department>> {
    return await Promise.all(ids.map((id) => this.findByPk(id)));
  }
}
