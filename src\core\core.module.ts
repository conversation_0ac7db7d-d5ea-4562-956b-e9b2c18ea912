import { Modu<PERSON> } from '@nestjs/common';
import { RequestModule } from './request/request.module';
import { SecurityModule } from './security/security.module';
import { StoreModule } from './store/store.module';
import { ComplaintModule } from './complaint/complaint.module';
import { NotificationModule } from './notification/notification.module';
import { CloudinaryService } from './storage/cloudinary/cloudinary.service';
import { LoggerModule } from '@common/logger/logger.module';
import { LogModule } from './log/log.module';

@Module({
  imports: [
    RequestModule,
    SecurityModule,
    StoreModule,
    ComplaintModule,
    NotificationModule,
    LoggerModule,
    LogModule,
  ],
  providers: [CloudinaryService],
})
export class CoreModule {}
