import { Column, Entity, OneToOne } from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { AbstractEntity } from '../../../../common/entities/base.entity';
import { User } from '../../user/entities/user.entity';
import { Store } from '../../../store/entities/store.entity';

@Entity({ name: 'address' })
export class Address extends AbstractEntity {
  @AutoMap()
  @Column({ name: 'latitude' })
  latitude: string;

  @AutoMap()
  @Column({ name: 'longitude' })
  longitude: string;

  @AutoMap()
  @Column({ name: 'house_address' })
  houseAddress: string;

  @AutoMap()
  @Column({ name: 'country' })
  country: string;

  @AutoMap()
  @Column({ name: 'state' })
  state: string;

  @AutoMap()
  @Column({ name: 'postal_code' })
  postalCode: string;

  @AutoMap()
  @OneToOne(() => User, (user) => user.address)
  user: User;

  @AutoMap()
  @OneToOne(() => Store, (store) => store.location)
  store: Store;
}
