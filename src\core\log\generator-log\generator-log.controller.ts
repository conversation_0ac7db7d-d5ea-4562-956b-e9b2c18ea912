import { CurrentRoute } from '@common/decorators/current-route.decorator';
import { CurrentUser } from '@common/decorators/current-user.decorator';
import { CoreUtils } from '@common/utils/core.utils';
import { User } from '@core/security/user/entities/user.entity';
import {
  Body,
  Controller,
  DefaultValuePipe,
  Get,
  HttpStatus,
  NotFoundException,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Req,
} from '@nestjs/common';
import { ApiBody, ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Pagination } from 'nestjs-typeorm-paginate';
import { ActivateGeneratorLogDto } from './dto/activate-generator-log.dto';
import { CreateGeneratorLogDto } from './dto/create-generator-log.dto';
import { DeactivateGeneratorLogDto } from './dto/deactivate-generator-log.dto';
import { GeneratorLogDto } from './dto/generator-log.dto';
import { GeneratorLogService } from './generator-log.service';
import { LoggerService } from '@common/logger/logger.service';
import { InjectMapper } from '@automapper/nestjs';
import { Mapper } from '@automapper/core';
import { GeneratorLog } from '@core/log/generator-log/entities/generator-log.entity';

@ApiTags('Generator Log')
@Controller({
  path: 'generator-log',
  version: '1',
})
export class GeneratorLogController {
  constructor(
    private readonly generatorLogService: GeneratorLogService,
    private readonly logger: LoggerService,
    @InjectMapper() private readonly classMapper: Mapper,
  ) {
    this.logger.setContext(GeneratorLogController.name);
  }

  @ApiOperation({ summary: 'Create a new generator log' })
  @ApiBody({ type: CreateGeneratorLogDto })
  @Post('new')
  async newGeneratorLog(
    @Body() createGeneratorLogDto: CreateGeneratorLogDto,
    @CurrentUser() user: User,
  ) {
    return CoreUtils.handleRequest(async () => {
      const generatorLog = await this.classMapper.mapAsync(
        createGeneratorLogDto,
        CreateGeneratorLogDto,
        GeneratorLog,
      );
      generatorLog.createdBy = `${user.firstName} ${user.lastName}`;
      await this.generatorLogService.create(generatorLog);
      return {
        message: 'Generator log created successfully',
        data: null,
      };
    });
  }

  @ApiOperation({ summary: 'Update an existing generator log' })
  @Patch('update/:id')
  async updateGeneratorLog(
    @Body() updateGeneratorDto: GeneratorLogDto,
    @Param('id') id: number,
    @CurrentUser() user: User,
  ) {
    return CoreUtils.handleRequest(async () => {
      const generatorLog = await this.generatorLogService.findByPk(id);
      if (!generatorLog) {
        throw new NotFoundException('Generator Log not found');
      }

      await this.classMapper.mutateAsync(
        updateGeneratorDto,
        generatorLog,
        GeneratorLogDto,
        GeneratorLog,
      );

      generatorLog.createdBy = `${user.firstName} ${user.lastName}`;

      await this.generatorLogService.update(generatorLog);
      return {
        message: 'Generator log updated successfully',
        data: null,
      };
    });
  }

  @ApiOperation({ summary: 'Retrieve a generator log' })
  @Get('detail/:id')
  async retrieveGeneratorLog(@Param('id') id: number) {
    return CoreUtils.handleRequest(async () => {
      const generatorLog: GeneratorLog =
        await this.generatorLogService.findByPk(id);
      await this.classMapper.mapAsync(
        generatorLog,
        GeneratorLog,
        GeneratorLogDto,
      );
      return {
        message: 'Generator log retrieved successfully',
        generatorLog,
      };
    });
  }

  @ApiOperation({ summary: 'Activate a generator log' })
  @ApiBody({ type: ActivateGeneratorLogDto })
  @Patch('activate')
  async activate(@Body() body: ActivateGeneratorLogDto, @Req() request: any) {
    return CoreUtils.handleRequest(async () => {
      await this.generatorLogService.activate(body.ids);
      return {
        message: `Generator log${
          body.ids.length > 1 ? 's' : ''
        } activated successfully`,
        data: null,
      };
    });
  }

  @ApiOperation({ summary: 'Deactivate a generator log' })
  @ApiBody({ type: DeactivateGeneratorLogDto })
  @Patch('deactivate')
  async deactivate(
    @Body() body: DeactivateGeneratorLogDto,
    @Req() request: any,
  ) {
    return CoreUtils.handleRequest(async () => {
      await this.generatorLogService.deactivate(body.ids);
      return {
        message: `Generator log${
          body.ids.length > 1 ? 's' : ''
        } deactivated successfully`,
        data: null,
      };
    });
  }

  @ApiOperation({ summary: 'Get paginated list of generator logs' })
  @ApiQuery({ name: 'page', required: true, type: Number, example: 1 })
  @ApiQuery({ name: 'limit', required: true, type: Number, example: 10 })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'filter', required: false, type: String })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Successful response with paginated generator logs',
    type: Pagination,
  })
  @Get()
  async getAllGeneratorLogs(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit,
    @Query('search') search: string,
    @Query('filter') filter: string,
    @CurrentRoute() route: string,
  ) {
    return CoreUtils.handleRequest(async () => {
      const data = await this.generatorLogService.getPaginatedGeneratorLogs(
        {
          page,
          limit,
          search,
          filter,
        },
        route,
      );
      return { message: 'Generator logs retrieved successfully', data };
    });
  }

  // server-side search

  // 1) Without pagination
  @ApiOperation({ summary: 'Search generator logs' })
  @Get('/search')
  async searchGeneratorLog(@Query('q') query: string) {
    return CoreUtils.handleRequest(async () => {
      const data = await this.generatorLogService.search(query);
      return {
        message: 'Generator logs retrieved successfully',
        data,
      };
    });
  }

  // 2) With pagination
  @ApiOperation({ summary: 'Paginate search generator logs' })
  @Get('/paginate-search')
  async paginateGeneratorLogSearch(
    @Query('q') query: string,
    @Query('page') page: number,
    @Query('limit') limit: number,
  ) {
    return CoreUtils.handleRequest(async () => {
      const data = await this.generatorLogService.paginatedSearch(
        query,
        page,
        limit,
      );
      return {
        message: 'Generator logs retrieved successfully',
        data,
      };
    });
  }
}
