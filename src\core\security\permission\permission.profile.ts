import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import { createMap, Mapper, MappingProfile } from '@automapper/core';
import { PermissionDto } from './dto/permission.dto';
import { Permission } from './entities/permission.entity';
import { CreatePermissionDto } from './dto/create-permission.dto';
import { UserPermissionDto } from './dto/user-permission.dto';

@Injectable()
export class PermissionProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile(): MappingProfile {
    return (mapper) => {
      createMap(mapper, Permission, PermissionDto);
      createMap(mapper, PermissionDto, Permission);
      createMap(mapper, CreatePermissionDto, Permission);
      createMap(mapper, Permission, UserPermissionDto);
      createMap(mapper, UserPermissionDto, Permission);
    };
  }
}
