import { ApiProperty } from '@nestjs/swagger';
import { AutoMap } from '@automapper/classes';
import { IsEmail, IsString } from 'class-validator';

export class PasswordResetDto {
  @AutoMap()
  @ApiProperty()
  @IsEmail()
  email: string;

  @AutoMap()
  @ApiProperty()
  @IsString()
  newPassword: string;

  @AutoMap()
  @ApiProperty()
  @IsString()
  defaultPassword: string;

  @AutoMap()
  @ApiProperty()
  @IsString()
  token: string;
}
