import { MigrationInterface, QueryRunner, TableForeign<PERSON><PERSON> } from 'typeorm';

export class CreateRequestAuditFkTable1752654513453
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create FX for request_audit table
    await queryRunner.createForeignKey(
      'request_audit',
      new TableForeignKey({
        name: 'FK_REQUEST_AUDIT_REQUEST_ID',
        columnNames: ['request_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'request_audit',
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the foreign key first
    const table = await queryRunner.getTable('request_audit');
    const foreignKey = table.foreignKeys.find(
      (fk) => fk.columnNames.indexOf('request_id') !== -1,
    );
    await queryRunner.dropForeignKey('request_audit', foreignKey);
  }
}
