import {
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Injectable,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { Reflector } from '@nestjs/core';
import { CaslAbilityFactoryService } from '../casl-ability-factory/casl-ability-factory.service';
import {
  CHECK_ABILITY,
  RequiredRule,
} from '../../../../common/decorators/abilities.decorator';
import { ForbiddenError } from '@casl/ability';
import { LoggerService } from '../../../../common/logger/logger.service';
import { IS_PUBLIC_KEY } from '../../../../common/decorators/public.decorator';

@Injectable()
export class AbilityGuard implements CanActivate {
  constructor(
    private readonly reflector: Reflector,
    private readonly caslAbilityFactoryService: CaslAbilityFactoryService,
    private readonly logger: LoggerService,
  ) {
    this.logger.setContext(AbilityGuard.name);
  }

  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    const rules =
      this.reflector.get<Array<RequiredRule>>(
        CHECK_ABILITY,
        context.getHandler(),
      ) || [];

    // Check if the route is marked as public
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      // If it's a public route, skip ability checks and allow access
      return true;
    }

    const { user } = context.switchToHttp().getRequest();


    // Generate abilities for the user
    const ability = this.caslAbilityFactoryService.createForUser(user);

    try {
      rules.forEach((rule) =>
        ForbiddenError.from(ability).throwUnlessCan(rule.action, rule.subject),
      );
      return true;
    } catch (error) {
      if (error instanceof ForbiddenError) {
        this.logger.error(error.message);
        throw new ForbiddenException(error.message);
      }
    }
  }
}
