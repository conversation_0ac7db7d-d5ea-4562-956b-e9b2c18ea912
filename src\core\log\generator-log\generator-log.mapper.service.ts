import { createMap, Mapper, MappingProfile } from '@automapper/core';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import { CreateGeneratorLogDto } from './dto/create-generator-log.dto';
import { GeneratorLogDto } from './dto/generator-log.dto';
import { GeneratorLog } from './entities/generator-log.entity';

@Injectable()
export class GeneratorLogMapperService extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile(): MappingProfile {
    return (mapper) => {
      createMap(mapper, GeneratorLog, GeneratorLogDto);
      createMap(mapper, GeneratorLogDto, GeneratorLog);
      createMap(mapper, CreateGeneratorLogDto, GeneratorLog);
    };
  }
}
