import { Module } from '@nestjs/common';
import { User } from '@core/security/user/entities/user.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AutomapperModule } from '@automapper/nestjs';
import { UserTableSeeder } from './user.seeder';
import { RoleModule } from '@core/security/role/role.module';
import { Role } from '@core/security/role/entities/role.entity';
import { StoreModule } from '@core/store/store.module';
import { Store } from '@core/store/entities/store.entity';
import { LoggerModule } from '@common/logger/logger.module';
import { Department } from '@core/security/department/entities/department.entity';
import { DepartmentModule } from '@core/security/department/department.module';

@Module({
  exports: [UserTableSeeder],
  imports: [
    AutomapperModule,
    TypeOrmModule.forFeature([User, Role, Department, Store]),
    RoleModule,
    DepartmentModule,
    StoreModule,
    LoggerModule,
  ],
  providers: [UserTableSeeder],
})
export class UserSeederModule {}
