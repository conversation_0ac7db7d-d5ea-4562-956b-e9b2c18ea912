import { AbstractEntity } from '@common/entities/base.entity';
import { Column, <PERSON>tity, JoinColumn, OneToOne, Unique } from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { Role } from '../../role/entities/role.entity';
import { Address } from '../../address/entities/address.entity';
import { Gender } from '@common/enums/gender.enum';
import { IsEmail } from 'class-validator';
import { Department } from '@core/security/department/entities/department.entity';

@Entity({ name: 'user' })
@Unique(['email'])
export class User extends AbstractEntity {
  @AutoMap()
  @Column({ name: 'first_name' })
  firstName: string;

  @AutoMap()
  @Column({ name: 'last_name' })
  lastName: string;

  @AutoMap()
  @IsEmail()
  @Column({ name: 'email' })
  email: string;

  @AutoMap()
  @Column({ name: 'phone_number' })
  phoneNumber: string;

  @AutoMap()
  @Column({ name: 'password' })
  password: string;

  @AutoMap(() => String)
  @Column({
    name: 'gender',
    type: 'enum',
    enum: Gender,
    default: Gender.DEFAULT,
  })
  gender: Gender;

  @AutoMap()
  @OneToOne(() => Department, {
    eager: true,
  })
  @JoinColumn({ name: 'department_id', referencedColumnName: 'id' })
  department: Department;

  @AutoMap(() => Role)
  @OneToOne(() => Role, { eager: true })
  @JoinColumn({ name: 'role_id', referencedColumnName: 'id' })
  role: Role;

  @AutoMap()
  @OneToOne(() => Address, (address) => address.user, {
    orphanedRowAction: 'delete',
    eager: true,
  })
  @JoinColumn({ name: 'address_id', referencedColumnName: 'id' })
  address: Address;

  @AutoMap()
  @Column({ name: 'token' })
  token: string;

  @AutoMap()
  @Column({ name: 'expires_at' })
  expiresAt: number;

  @AutoMap()
  @Column({
    name: 'refresh_token',
    nullable: true,
    type: 'text',
    default: null,
  })
  refreshToken: string;

  @AutoMap()
  @Column({ name: 'is_verified', type: 'boolean', default: false })
  isVerified: boolean;

  @AutoMap()
  @Column({ name: 'has_default_password', type: 'boolean', default: true })
  hasDefaultPassword: boolean;
}
