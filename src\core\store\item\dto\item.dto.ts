import { AutoMap } from '@automapper/classes';
import { EntityDto } from '@common/dto/base.dto';
import { DepartmentDto } from '@core/security/department/dto/department.dto';
import { ItemUnitDto } from '@core/store/item-unit/dto/item-unit.dto';
import { ApiProperty } from '@nestjs/swagger';

export class ItemDto extends EntityDto {
  @AutoMap()
  @ApiProperty()
  name: string;

  @AutoMap(() => Number)
  @ApiProperty()
  actualQuantity: number;

  @AutoMap(() => Number)
  @ApiProperty()
  availableQuantity: number;

  @AutoMap()
  @ApiProperty({
    type: Boolean,
    default: false,
  })
  fragile: boolean;

  @AutoMap(() => DepartmentDto)
  @ApiProperty()
  department: DepartmentDto;

  @AutoMap(() => [ItemUnitDto])
  @ApiProperty()
  itemUnits?: ItemUnitDto[];
}
