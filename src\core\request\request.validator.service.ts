import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DatabaseAction } from '@common/enums/dbaction.enum';
import { BaseValidator } from '@common/validator/base.validator';
import { Request } from './entities/request.entity';

@Injectable()
export class RequestValidatorService implements BaseValidator<Request> {
  constructor(
    @InjectRepository(Request) private requestRepository: Repository<Request>,
  ) {}

  async validate(data: Request, action: DatabaseAction) {
    const existing: Request = await this.requestRepository.findOne({
      where: { id: data.id },
    });

    if (DatabaseAction.CREATE == action) {
      // throw new Error('Request already exists.');
      return;
    } else if (
      DatabaseAction.UPDATE === action &&
      existing &&
      existing.id !== data.id
    ) {
      throw new NotFoundException('Request not found.');
    }
  }
}
