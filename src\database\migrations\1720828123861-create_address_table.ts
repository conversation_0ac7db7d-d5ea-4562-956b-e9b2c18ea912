import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateAddressTable1720828123861 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Address Table.
    await queryRunner.createTable(
      new Table({
        name: 'address',
        columns: [
          {
            name: 'id',
            type: 'bigint',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'latitude',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'longitude',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'house_address',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'country',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'state',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'postal_code',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'status',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'now()',
          },
          {
            name: 'created_by',
            type: 'varchar',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'updated_by',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'deleted_at',
            type: 'timestamp',
            isNullable: true,
          },
        ],
      }),
      true,
    );

    // Add an index to the address table.
    await queryRunner.createIndex(
      'address',
      new TableIndex({
        name: 'IDX_HOUSE_ADDRESS',
        columnNames: ['house_address', 'latitude', 'longitude', 'postal_code'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    //Drop address table and its index
    await queryRunner.dropIndex('address', 'IDX_HOUSE_ADDRESS');
    await queryRunner.dropTable('address');
  }
}
