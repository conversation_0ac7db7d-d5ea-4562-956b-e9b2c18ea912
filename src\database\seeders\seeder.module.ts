import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Seeder } from './seeder';
import configuration from '../../config';
import { PermissionSeederModule } from './permission-seeder/permission-seeder.module';
import { UserSeederModule } from './user-seeder/user-seeder.module';
import { LoggerModule } from '../../common/logger/logger.module';

/**
 * Import and provide seeder classes.
 * @module
 */
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [configuration],
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        ...configService.get('database'),
        autoLoadEntities: true,
      }),
      inject: [ConfigService],
    }),
    UserSeederModule,
    PermissionSeederModule,
    LoggerModule,
  ],
  providers: [Seeder],
})
export class SeedersModule {}
