import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import { createMap, forMember, mapFrom, Mapper, MappingProfile } from '@automapper/core';
import { RequestAudit } from './entities/request-audit.entity';
import { RequestAuditDto } from './dto/request-audit.dto';
import { CreateRequestAuditDto } from '@core/request/request-audit/dto/create-request-audit.dto';
import { ItemAudit } from '@core/store/item-audit/entities/item-audit.entity';
import { ItemAuditDto } from '@core/store/item-audit/dto/item-audit.dto';

@Injectable()
export class RequestAuditMapperService extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile(): MappingProfile {
    return (mapper) => {
      createMap(
        mapper,
        RequestAudit,
        RequestAuditDto,
        forMember(
          (destination) => destination.items,
          mapFrom((source) =>
            this.mapper.mapArray(source.items, ItemAudit, ItemAuditDto),
          ),
        ),
      );
      createMap(mapper, CreateRequestAuditDto, RequestAudit);
    };
  }
}
