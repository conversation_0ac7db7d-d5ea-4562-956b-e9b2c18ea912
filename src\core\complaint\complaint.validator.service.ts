import { BaseValidator } from '@common/validator/base.validator';
import { Complaint } from './entities/complaint.entity';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DatabaseAction } from '@common/enums/dbaction.enum';

@Injectable()
export class ComplaintValidatorService implements BaseValidator<Complaint> {
  constructor(
    @InjectRepository(Complaint)
    private generatorLogRepository: Repository<Complaint>,
  ) {}

  async validate(data: Complaint, action: DatabaseAction) {
    const existing: Complaint = await this.generatorLogRepository.findOne({
      where: { id: data.id },
    });

    if (DatabaseAction.CREATE == action) {
      return;
    } else if (
      DatabaseAction.UPDATE === action &&
      existing &&
      existing.id !== data.id
    ) {
      throw new NotFoundException('Complaint not found.');
    }
  }
}
