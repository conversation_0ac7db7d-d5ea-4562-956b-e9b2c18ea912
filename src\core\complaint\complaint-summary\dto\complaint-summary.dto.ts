import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { EntityDto } from '@common/dto/base.dto';

export class ComplaintSummaryDto extends EntityDto {
  @AutoMap()
  @ApiProperty()
  complaintStatus: string;

  @AutoMap()
  @ApiProperty()
  attendedTo: boolean;

  @AutoMap()
  @ApiProperty()
  dateResolved: Date;

  @AutoMap()
  @ApiProperty()
  resolvedBy: string;
}
