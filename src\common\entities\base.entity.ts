import { AutoMap } from '@automapper/classes';
import {
  BaseEntity,
  BeforeInsert,
  BeforeSoftRemove,
  BeforeUpdate,
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum EntityStatus {
  ACTIVE = 'A',
  INACTIVE = 'I',
}

export abstract class AbstractEntity extends BaseEntity {
  @AutoMap()
  @PrimaryGeneratedColumn()
  id: number;

  @AutoMap(() => String)
  @Column({
    name: 'status',
    type: 'enum',
    enum: EntityStatus,
    default: EntityStatus.ACTIVE,
  })
  status: string;

  @AutoMap()
  @CreateDateColumn({ name: 'created_at' })
  createdAt!: Date;

  @AutoMap()
  @Column({ name: 'created_by' })
  createdBy: string;

  @AutoMap()
  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt!: Date;

  @AutoMap()
  @Column({ name: 'updated_by' })
  updatedBy: string;

  @AutoMap()
  @DeleteDateColumn({ name: 'deleted_at' })
  deletedAt?: Date;

  @BeforeInsert()
  preInsert() {
    this.status = this.status || EntityStatus.ACTIVE;
    this.createdBy = this.createdBy || 'SYSTEM';
    this.createdAt = new Date();
  }

  @BeforeUpdate()
  preUpdate() {
    this.updatedBy = this.updatedBy || 'SYSTEM';
    this.updatedAt = new Date();
  }

  @BeforeSoftRemove()
  preDelete() {
    this.deletedAt = new Date();
  }
}
