import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsNumber, IsString } from 'class-validator';

export class CreateItemDto {
  @AutoMap()
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  name: string;

  @AutoMap(() => Number)
  @ApiProperty()
  @IsNumber()
  actualQuantity: number;

  @AutoMap()
  @ApiProperty({
    type: Boolean,
    default: false,
  })
  @IsBoolean()
  fragile: boolean;

  @AutoMap(() => Number)
  @ApiProperty()
  @IsNumber()
  departmentId: number;
}
