import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { Condition } from '@common/enums/condition.enum';
import { IsDate, IsEnum, IsNumber, IsString } from 'class-validator';

export class ReturnItemAuditDto {
  @AutoMap()
  @ApiProperty()
  @IsNumber()
  quantityReturned: number;

  @AutoMap()
  @ApiProperty()
  @IsString()
  returnedDate: Date;

  @AutoMap(() => String)
  @ApiProperty({
    type: 'string',
    enum: Object.values(Condition),
    name: 'conditionAfterReturned',
    description: 'Possible item conditions',
  })
  @IsEnum(Condition)
  conditionAfterReturned: Condition;
}
