import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddChurchLetterToRequestTable1752886387409
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add church_letter column to request table
    await queryRunner.addColumn(
      'request',
      new TableColumn({
        name: 'church_letter',
        type: 'jsonb',
        isNullable: true,
        default: null,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove church_letter column from request table
    await queryRunner.dropColumn('request', 'church_letter');
  }
}
