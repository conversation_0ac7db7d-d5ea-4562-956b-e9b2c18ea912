import { DatabaseAction } from 'src/common/enums/dbaction.enum';
import { Repository } from 'typeorm';
import { User } from './entities/user.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { BaseValidator } from '../../../common/validator/base.validator';
import { Injectable } from '@nestjs/common';

@Injectable()
export class UserValidator implements BaseValidator<User> {
  constructor(
    @InjectRepository(User) private userRepository: Repository<User>,
  ) {}

  async validate(data: User, action: DatabaseAction) {
    const existing: User = await this.userRepository.findOne({
      where: { email: data.email },
    });

    if (DatabaseAction.CREATE == action) {
      return;
    } else if (
      DatabaseAction.UPDATE === action &&
      existing &&
      existing.id !== data.id
    ) {
      throw new Error('User not found.');
    }
  }
}
