// eslint-disable-next-line @typescript-eslint/no-var-requires
const rawData = require('./data from data science team/Electrical.json');

const processData = (data = []) => {
  return data.map((item) => {
    // Ensure item.fragile is a string or default to an empty string
    const fragile = typeof item.fragile === 'string' ? item.fragile.trim() : '';

    const processedItem = {
      ...item,
      name: item.name.trim(),
      storeId: parseInt(item.storeId),
      departmentId: parseInt(item.departmentId),
      actualQuantity:
        item.actualQuantity !== undefined
          ? parseInt(item.actualQuantity)
          : parseInt(item.quantity),
      condition:
        item.condition.trim() === 'GOOD' ||
        item.condition.trim() === 'Good' ||
        item.condition.trim() === 'good'
          ? 'Good'
          : 'Bad',
      fragile: fragile === 'YES' || fragile === 'Yes' || fragile === 'yes',
    };

    // Remove the original quantity field if actualQuantity was set
    if (item.actualQuantity === undefined) {
      delete processedItem.quantity;
    }

    return processedItem;
  });
};
const updatedData = processData(rawData);

console.log(JSON.stringify(updatedData, null, 2)); // Added indentation for readability
