import json

# Load the JSON data from a file
with open('./data from data science team/Food service.json') as f:
    raw_data = json.load(f)

def process_data(data):
    processed_data = []
    for item in data:
        # Ensure item['fragile'] is a string or default to an empty string
        fragile = item.get('fragile', '')
        if isinstance(fragile, str):
            fragile = fragile.strip()
        else:
            fragile = ''

        processed_item = {
            **item,
            'name': item.get('name', '').strip(),
            'storeId': int(item.get('storeId', 0)),
            'departmentId': int(item.get('departmentId', 0)),
            'actualQuantity': int(item.get('actualQuantity', item.get('quantity', 0))),
            'condition': 'Good' if item.get('condition', '').strip().lower() in ['good', 'good'] else 'Bad',
            'fragile': fragile.lower() in ['yes', 'yes']
        }

        # Remove the original quantity field if actualQuantity was set
        if 'actualQuantity' in item:
            processed_item.pop('quantity', None)

        processed_data.append(processed_item)

    return processed_data

updated_data = process_data(raw_data)

# Print the JSON data with indentation for readability
print(json.dumps(updated_data, indent=2))
