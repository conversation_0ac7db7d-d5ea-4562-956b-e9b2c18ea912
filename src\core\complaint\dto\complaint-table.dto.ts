import { AutoMap } from '@automapper/classes';
import { EntityDto } from '@common/dto/base.dto';
import { ApiProperty } from '@nestjs/swagger';
import { ComplaintSummaryDto } from '@core/complaint/complaint-summary/dto/complaint-summary.dto';

export class ComplaintTableDto extends EntityDto {
  @AutoMap()
  @ApiProperty()
  complaintSubject: string;

  @AutoMap()
  @ApiProperty()
  complaintDescription: string;

  @AutoMap()
  @ApiProperty()
  complaintDate: Date;

  @AutoMap()
  @ApiProperty()
  summary: ComplaintSummaryDto;
}
