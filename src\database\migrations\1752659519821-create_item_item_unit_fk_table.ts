import { MigrationInterface, QueryRunner, TableForeign<PERSON>ey } from 'typeorm';

export class CreateItemItemUnitFkTable1752659519821
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createForeignKeys('item_unit', [
      new TableForeignKey({
        name: 'FK_ITEM_UNIT_ITEM_ID',
        columnNames: ['item_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'item',
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      }),
      new TableForeignKey({
        name: 'FK_ITEM_UNIT_STORE_ID',
        columnNames: ['store_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'store',
      }),
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const table = await queryRunner.getTable('item_unit');
    const foreignKeys = table.foreignKeys.filter(
      (fk) =>
        fk.columnNames.indexOf('item_id') !== -1 ||
        fk.columnNames.indexOf('store_id') !== -1,
    );

    for (const foreignKey of foreignKeys) {
      await queryRunner.dropForeignKey('item_unit', foreignKey);
    }
  }
}
