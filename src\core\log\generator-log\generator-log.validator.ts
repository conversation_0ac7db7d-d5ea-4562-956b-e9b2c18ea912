import { GeneratorLog } from './entities/generator-log.entity';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseValidator } from '@common/validator/base.validator';
import { DatabaseAction } from '@common/enums/dbaction.enum';

@Injectable()
export class GeneratorLogValidator implements BaseValidator<GeneratorLog> {
  constructor(
    @InjectRepository(GeneratorLog)
    private generatorLogRepository: Repository<GeneratorLog>,
  ) {}

  async validate(data: GeneratorLog, action: DatabaseAction) {
    const existing: GeneratorLog = await this.generatorLogRepository.findOne({
      where: { id: data.id },
    });

    if (DatabaseAction.CREATE == action) {
      return;
    } else if (
      DatabaseAction.UPDATE === action &&
      existing &&
      existing.id !== data.id
    ) {
      throw new Error('Generator log not found.');
    }
  }
}
