import { AutoMap } from '@automapper/classes';
import { Condition } from '@common/enums/condition.enum';
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';
import { ItemUnitAudit } from '@core/store/item-audit/dto/type/item-unit-audit.type';
import { ItemUnitAuditDto } from '@core/store/item-audit/dto/item-unit-audit.dto';

export class CreateItemAuditDto {
  @AutoMap()
  @ApiProperty({
    type: Number,
    description: 'Unique identifier for the item',
    example: 1,
    name: 'itemId',
  })
  @IsNumber()
  itemId: number;

  @AutoMap()
  @ApiProperty({
    type: Number,
    description: 'The units of the item leased out',
    example: 10,
    name: 'quantityLeased',
  })
  @IsNumber()
  @IsOptional()
  quantityLeased: number;

  @AutoMap()
  @ApiProperty({
    type: Number,
    description: 'The units of the item released',
    example: 5,
    name: 'quantityReleased',
  })
  @IsNumber()
  @IsOptional()
  quantityReleased?: number;

  @AutoMap()
  @ApiProperty({
    type: Number,
    description: 'The units of the item returned',
    example: 3,
    name: 'quantityReturned',
  })
  @IsNumber()
  @IsOptional()
  quantityReturned?: number;

  // @AutoMap(() => String)
  // @ApiProperty({
  //   type: 'string',
  //   enum: Object.values(Condition),
  //   name: 'conditionBeforeLease',
  //   description: 'Possible item conditions',
  //   default: Condition.NOT_SPECIFIED,
  // })
  // @IsEnum(Condition)
  // @IsOptional()
  // conditionBeforeLease: Condition;

  @AutoMap()
  @ApiProperty({
    type: Date,
    description: 'Date when the item was leased out',
    example: '2023-10-01T12:00:00Z',
    name: 'leasedDate',
  })
  @IsString()
  @IsOptional()
  leasedDate: Date;

  @AutoMap()
  @ApiProperty({
    type: Date,
    description: 'Date when the item was returned',
    example: '2023-10-02T12:00:00Z',
    name: 'returnedDate',
  })
  @IsString()
  @IsOptional()
  returnedDate?: Date;

  @AutoMap()
  @ApiProperty({
    type: ItemUnitAuditDto,
    isArray: true,
    example: [
      {
        storeId: 1,
        storeName: 'Main Store',
        serialNumber: 'EGFM/USH/OFF/009',
        condition: Condition.NOT_SPECIFIED,
      },
    ],
  })
  units: ItemUnitAuditDto[];
}
