import { NestFactory } from '@nestjs/core';
import { SeedersModule } from './database/seeders/seeder.module';
import { Seeder } from './database/seeders/seeder';

async function bootstrap() {
  NestFactory.createApplicationContext(SeedersModule)
    .then((appContext) => {
      const seeder = appContext.get(Seeder);
      seeder
        .seed()
        .then(() => {
          console.log('Seeding completed successfully');
        })
        .catch((error) => {
          console.error('Seeding failed');
          throw error;
        })
        .finally(() => {
          console.log('Closing application context');
          appContext.close();
        });
    })
    .catch((error) => {
      console.error('Failed to create application context');
      throw error;
    });
}
bootstrap();
