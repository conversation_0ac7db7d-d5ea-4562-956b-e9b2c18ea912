import { CreateUserTable1720827482104 } from '@database/migrations/1720827482104-create_user_table';
import { CreateAddressTable1720828123861 } from '@database/migrations/1720828123861-create_address_table';
import { CreateUserAddressFkTable1720828301585 } from '@database/migrations/1720828301585-create_user_address_fk_table';
import { CreateRoleTable1720828418045 } from '@database/migrations/1720828418045-create_role_table';
import { CreatePermissionTable1720828502524 } from '@database/migrations/1720828502524-create_permission_table';
import { CreateStoreTable1720828626221 } from '@database/migrations/1720828626221-create_store_table';
import { CreateStoreAddressFkTable1720828861356 } from '@database/migrations/1720828861356-create_store_address_fk_table';
import { CreateMaintenanceLogTable1752616911393 } from '@database/migrations/1752616911393-create_maintenance_log_table';
import { CreateRequestTable1752617260389 } from '@database/migrations/1752617260389-create_request_table';
import { CreateRequestAuditTable1752654147450 } from '@database/migrations/1752654147450-create_request_audit_table';
import { CreateItemTable1752657448902 } from '@database/migrations/1752657448902-create_item_table';
import { CreateDepartmentTable1752658086411 } from '@database/migrations/1752658086411-create_department_table';
import { CreateDepartmentItemFkTable1752658760572 } from '@database/migrations/1752658760572-create_department_item_fk_table';
import { CreateRequestAuditFkTable1752654513453 } from '@database/migrations/1752654513453-create_request_audit_fk_table';
import { CreateItemUnitTable1752659001539 } from '@database/migrations/1752659001539-create_item_unit_table';
import { CreateItemItemUnitFkTable1752659519821 } from '@database/migrations/1752659519821-create_item_item_unit_fk_table';
import { CreateGeneratorLogTable1752659851935 } from '@database/migrations/1752659851935-create_generator_log_table';
import { CreateItemAuditTable1752660288922 } from '@database/migrations/1752660288922-create_item_audit_table';
import { CreateItemAuditRequestAuditFkTable1752660688370 } from '@database/migrations/1752660688370-create_item_audit_request_audit_fk_table';
import { CreateUserRoleFkTable1752662058100 } from '@database/migrations/1752662058100-create_user_role_fk_table';
import { CreateUserDepartmentFkTable1752662735583 } from '@database/migrations/1752662735583-create_user_department_fk_table';
import { CreateComplaintComplaintSummaryFk1752663486437 } from '@database/migrations/1752663486437-create_complaint_complaint_summary_fk';
import { AddChurchLetterToRequestTable1752886387409 } from '@database/migrations/1752886387409-add_church_letter_to_request_table';

export default [
  CreateUserTable1720827482104,
  CreateAddressTable1720828123861,
  CreateUserAddressFkTable1720828301585,
  CreateRoleTable1720828418045,
  CreatePermissionTable1720828502524,
  CreateStoreTable1720828626221,
  CreateStoreAddressFkTable1720828861356,
  CreateMaintenanceLogTable1752616911393,
  CreateRequestTable1752617260389,
  CreateRequestAuditTable1752654147450,
  CreateRequestAuditFkTable1752654513453,
  CreateItemTable1752657448902,
  CreateDepartmentTable1752658086411,
  CreateDepartmentItemFkTable1752658760572,
  CreateItemUnitTable1752659001539,
  CreateItemItemUnitFkTable1752659519821,
  CreateGeneratorLogTable1752659851935,
  CreateItemAuditTable1752660288922,
  CreateItemAuditRequestAuditFkTable1752660688370,
  CreateUserRoleFkTable1752662058100,
  CreateUserDepartmentFkTable1752662735583,
  CreateComplaintComplaintSummaryFk1752663486437,
  AddChurchLetterToRequestTable1752886387409,
];
