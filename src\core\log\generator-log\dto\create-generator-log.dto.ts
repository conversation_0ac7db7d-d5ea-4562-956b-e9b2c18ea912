import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsString, IsOptional } from 'class-validator';

export class CreateGeneratorLogDto {
  @AutoMap()
  @ApiProperty()
  @IsString()
  nameOfMeeting: string;

  @AutoMap()
  @ApiProperty()
  @IsString()
  generatorType: string;

  @AutoMap()
  @ApiProperty()
  @IsString()
  meetingLocation: string;

  @AutoMap()
  @ApiProperty()
  @IsString()
  onTime: Date;

  @AutoMap()
  @ApiProperty()
  @IsString()
  offTime: Date;

  @AutoMap()
  @ApiProperty()
  @IsString()
  hoursUsed: Date;

  @AutoMap()
  @ApiProperty()
  @IsString()
  engineStartHours: string;

  @AutoMap()
  @ApiProperty()
  @IsString()
  engineOffHours: string;

  @AutoMap()
  @ApiProperty()
  @IsString()
  dieselLevelOn: string;

  @AutoMap()
  @ApiProperty()
  @IsString()
  dieselLevelOff: string;

  @AutoMap()
  @ApiProperty()
  @IsString()
  lastServiceHour: Date;

  @AutoMap()
  @ApiProperty()
  @IsString()
  @IsOptional()
  nextServiceHour: Date;

  @AutoMap()
  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  dueForService: boolean;

  @AutoMap()
  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  oilFilterDueForReplacement: boolean;

  @AutoMap()
  @ApiProperty()
  @IsString()
  @IsOptional()
  LastOilFilterReplacement: Date;

  @AutoMap()
  @ApiProperty()
  @IsOptional()
  faultDetected: boolean;

  @AutoMap()
  @ApiProperty()
  @IsString()
  @IsOptional()
  faultDescription: string;

  @AutoMap()
  @ApiProperty()
  @IsString()
  personnelName: string;

  @AutoMap()
  @ApiProperty()
  @IsString()
  @IsOptional()
  Remark: string;
}
