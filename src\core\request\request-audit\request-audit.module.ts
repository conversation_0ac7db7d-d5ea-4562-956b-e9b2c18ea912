import { Module } from '@nestjs/common';
import { RequestAuditMapperService } from './request-audit.mapper.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RequestAudit } from './entities/request-audit.entity';
import { AutomapperModule } from '@automapper/nestjs';

@Module({
  controllers: [],
  exports: [],
  imports: [TypeOrmModule.forFeature([RequestAudit]), AutomapperModule],
  providers: [RequestAuditMapperService],
})
export class RequestAuditModule {}
