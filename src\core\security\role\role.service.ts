import { Injectable } from '@nestjs/common';
import { Role } from './entities/role.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { EntityStatus } from '../../../common/entities/base.entity';
import {
  IPaginationOptions,
  paginate,
  paginateRaw,
  Pagination,
} from 'nestjs-typeorm-paginate';
import { LoggerService } from '../../../common/logger/logger.service';

@Injectable()
export class RoleService {
  constructor(
    @InjectRepository(Role) private roleRepository: Repository<Role>,
    private readonly logger: LoggerService,
  ) {
    this.logger.setContext(RoleService.name);
  }
  async create(role: Role): Promise<Role> {
    // run create validation here
    return this.roleRepository.save(role);
  }

  async findAll(): Promise<Role[]> {
    return this.roleRepository.find();
  }

  async findAllById(roles: number[]): Promise<Role[]> {
    return await this.roleRepository
      .createQueryBuilder('role')
      .where('role.id IN (:...ids)', { ids: roles })
      .getMany();
  }

  async findAllAvailableRoles(userId: number): Promise<Role[]> {
    return this.roleRepository
      .createQueryBuilder('role')
      .where(
        `role.id not in (select role_id from user_role where user_id = ${userId})`,
      )
      .getMany();
  }

  async findAllAssignedRoles(userId: number): Promise<Role[]> {
    return this.roleRepository
      .createQueryBuilder('role')
      .where(
        `role.id in (select role_id from user_role where user_id = ${userId})`,
      )
      .getMany();
  }

  async findByPk(id: number): Promise<Role> {
    return this.roleRepository.findOneBy({ id });
  }

  async findRoleByName(name: string): Promise<Role> {
    return this.roleRepository.findOneBy({ name });
  }

  async update(role: Role): Promise<Role> {
    // run update validation here
    return this.roleRepository.save(role);
  }

  async remove(id: number): Promise<void> {
    await this.roleRepository.delete(id);
  }

  async activate(ids: Array<number>): Promise<void> {
    ids.map(async (id) => {
      const role: Role = await this.findByPk(id);
      role.status = EntityStatus.ACTIVE;
      await this.update(role);
    });
  }

  async deactivate(ids: Array<number>): Promise<void> {
    ids.map(async (id) => {
      const role: Role = await this.findByPk(id);
      role.status = EntityStatus.INACTIVE;
      await this.update(role);
    });
  }

  async paginateRaw(
    queryBuilder: SelectQueryBuilder<Role>,
    options: IPaginationOptions,
    where?: any,
  ): Promise<Pagination<Role>> {
    if (where && Object.keys(where).length > 0) {
      return paginateRaw(queryBuilder, options);
    }
    return paginateRaw(queryBuilder, options);
  }

  async paginate(
    options: IPaginationOptions,
    where?: any,
  ): Promise<Pagination<Role>> {
    if (where && Object.keys(where).length > 0) {
      return paginate<Role>(this.roleRepository, options, {
        where,
        order: {
          createdAt: 'DESC',
        },
      });
    }
    return paginate<Role>(this.roleRepository, options);
  }

  async assignedRoles(
    userId: number,
    page: number,
    limit: number,
    search: string,
  ): Promise<Pagination<Role>> {
    let queryBuilder = this.roleRepository
      .createQueryBuilder('role')
      .where(
        `role.id in (select role_id from user_role where user_id = ${userId})`,
      );
    if (search) {
      queryBuilder = queryBuilder.andWhere(
        '(role.name like :search or role.description like :search)',
        { search: `%${search}%` },
      );
    }
    return await this.paginateRaw(queryBuilder, {
      page,
      limit,
      route: `/api/role/assigned`,
    });
  }

  async availableRoles(
    userId: number,
    page: number,
    limit: number,
    search: string,
  ): Promise<Pagination<Role>> {
    let queryBuilder = this.roleRepository
      .createQueryBuilder('role')
      .where(
        `role.id not in (select role_id from user_role where user_id = ${userId})`,
      );

    if (search) {
      queryBuilder = queryBuilder.andWhere(
        '(role.name like :search or role.description like :search)',
        { search: `%${search}%` },
      );
    }
    return await this.paginateRaw(queryBuilder, {
      page,
      limit,
      route: `/api/role/available`,
    });
  }

  delete(id: number): Promise<void> {
    return Promise.resolve(undefined);
  }

  paginatedSearch(
    query: string,
    page: number,
    limit: number,
  ): Promise<Array<Role>> {
    return Promise.resolve(undefined);
  }

  search(query: string): Promise<Array<Role>> {
    return Promise.resolve(undefined);
  }
}
