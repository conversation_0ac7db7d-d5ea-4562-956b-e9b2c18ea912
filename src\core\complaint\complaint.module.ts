import { Modu<PERSON> } from '@nestjs/common';
import { ComplaintService } from './complaint.service';
import { ComplaintController } from './complaint.controller';
import { ComplaintValidatorService } from './complaint.validator.service';
import { ComplaintMapperService } from './complaint.mapper.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Complaint } from './entities/complaint.entity';
import { AutomapperModule } from '@automapper/nestjs';
import { LoggerModule } from '../../common/logger/logger.module';

@Module({
  controllers: [ComplaintController],
  exports: [ComplaintService],
  imports: [
    TypeOrmModule.forFeature([Complaint]),
    AutomapperModule,
    LoggerModule,
  ],
  providers: [ComplaintService, ComplaintValidatorService, ComplaintMapperService],
})
export class ComplaintModule {}
