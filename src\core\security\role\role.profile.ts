import { Injectable } from '@nestjs/common';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import {
  createMap,
  forMember,
  mapFrom,
  Mapper,
  MappingProfile,
} from '@automapper/core';
import { Role } from './entities/role.entity';
import { RoleDto } from './dto/role.dto';
import { CreateRoleDto } from './dto/create-role.dto';
import { UserRoleDto } from './dto/user-role.dto';
import { LoggedInUserRoleDto } from './dto/logged-in-user-role.dto';
import { UserPermissionDto } from '../permission/dto/user-permission.dto';
import { Permission } from '../permission/entities/permission.entity';

@Injectable()
export class RoleProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile(): MappingProfile {
    return (mapper) => {
      createMap(mapper, Role, RoleDto);
      createMap(mapper, CreateRoleDto, Role);
      createMap(mapper, Role, UserRoleDto);
      createMap(
        mapper,
        Role,
        LoggedInUserRoleDto,
        forMember(
          (destination) => destination.permissions,
          mapFrom((source) => source.permissions),
        ),
      );
      createMap(mapper, Permission, UserPermissionDto);
      createMap(
        mapper,
        Role,
        String,
        forMember(
          (destination) => destination.name,
          mapFrom((source) => source.name),
        ),
      );
    };
  }
}
