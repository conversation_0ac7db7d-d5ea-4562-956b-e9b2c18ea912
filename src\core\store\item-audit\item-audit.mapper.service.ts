import { Injectable } from '@nestjs/common';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import {
  createMap,
  forMember,
  mapFrom,
  Mapper,
  MappingProfile,
} from '@automapper/core';
import { ItemAudit } from './entities/item-audit.entity';
import { CreateItemAuditDto } from './dto/create-item-audit.dto';
import { ReturnItemAuditDto } from './dto/return-item-audit.dto';
import { ItemAuditDto } from './dto/item-audit.dto';
import { RequestedItemDto } from '../item/dto/requested-item.dto';

@Injectable()
export class ItemAuditMapper extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile(): MappingProfile {
    return (mapper) => {
      createMap(
        mapper,
        CreateItemAuditDto,
        ItemAudit,
        forMember(
          (destination) => destination.itemId,
          mapFrom((source) => Number(source.itemId)),
        ),
        forMember(
          (destination) => destination.quantityLeased,
          mapFrom((source) => Number(source.quantityLeased)),
        ),
      );
      createMap(mapper, ReturnItemAuditDto, ItemAudit);
      createMap(mapper, ItemAuditDto, ItemAudit);
      createMap(
        mapper,
        ItemAudit,
        ItemAuditDto,
        forMember(
          (destination) => destination.itemId,
          mapFrom((source) => Number(source.itemId)),
        ),
        forMember(
          (destination) => destination.quantityLeased,
          mapFrom((source) => Number(source.quantityLeased)),
        ),
      );
      createMap(
        mapper,
        ItemAudit,
        RequestedItemDto,
        forMember(
          (destination) => destination.itemId,
          mapFrom((source) => Number(source.itemId)),
        ),
        forMember(
          (destination) => destination.quantityLeased,
          mapFrom((source) => Number(source.quantityLeased)),
        ),
      );
      createMap(mapper, CreateItemAuditDto, ItemAuditDto);
    };
  }
}
