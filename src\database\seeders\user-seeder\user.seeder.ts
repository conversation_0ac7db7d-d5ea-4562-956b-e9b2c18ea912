import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '@core/security/user/entities/user.entity';
import { Role } from '@core/security/role/entities/role.entity';
import { departments, roles, stores, users } from './data';
import { RoleDto } from '@core/security/role/dto/role.dto';
import { UserDto } from '@core/security/user/dto/user.dto';
import { Store } from '@core/store/entities/store.entity';
import { CreateStoreDto } from '@core/store/dto/create-store.dto';
import { LoggerService } from '@common/logger/logger.service';
import { CreateDepartmentDto } from '@core/security/department/dto/create-department.dto';
import { Department } from '@core/security/department/entities/department.entity';

@Injectable()
export class UserTableSeeder {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
    @InjectRepository(Department)
    private readonly departmentRepository: Repository<Department>,
    @InjectRepository(Store)
    private readonly storeRepository: Repository<Store>,
    private readonly logger: LoggerService,
  ) {
    this.logger.setContext(UserTableSeeder.name);
  }

  async seed(): Promise<void> {
    await Promise.all([
      this.createStores(),
      this.createDepartments(),
      this.createRoles(),
      this.createUsers(),
    ]);
  }

  public async createStores(): Promise<Array<CreateStoreDto>> {
    for (const store of stores) {
      try {
        const existingStore = await this.storeRepository.findOne({
          where: { name: store.name },
        });
        if (!existingStore) {
          const newStore = this.storeRepository.create(store);
          await this.storeRepository.save(newStore);
        }
      } catch (error) {
        this.logger.error(`Error creating store ${store.name}:`, error);
      }
    }
    return stores;
  }

  public async createDepartments(): Promise<Array<CreateDepartmentDto>> {
    for (const department of departments) {
      try {
        const existingDepartment = await this.departmentRepository.findOne({
          where: { name: department.name },
        });
        if (!existingDepartment) {
          const newDepartment = this.departmentRepository.create(department);
          await this.departmentRepository.save(newDepartment);
        }
      } catch (error) {
        this.logger.error(
          `Error creating department ${department.name}:`,
          error,
        );
      }
    }
    return departments;
  }

  public async createRoles(): Promise<Array<RoleDto>> {
    for (const role of roles) {
      try {
        const existingRole = await this.roleRepository.findOne({
          where: { name: role.name },
        });
        if (!existingRole) {
          const newRole = this.roleRepository.create(role);
          await this.roleRepository.save(newRole);
        }
      } catch (error) {
        this.logger.error(`Error creating role ${role.name}:`, error);
      }
    }
    return roles;
  }

  public async createUsers(): Promise<Array<UserDto>> {
    for (const user of users) {
      try {
        const existingUser = await this.userRepository.findOne({
          where: { email: user.email },
        });
        if (!existingUser) {
          const role = await this.roleRepository.findOne({
            where: { name: user.role },
          });
          if (!role) {
            this.logger.error(`Role with ID ${user.role} not found.`);
            continue;
          }

          const department = await this.departmentRepository.findOne({
            where: { name: user.department },
          });

          if (!department) {
            this.logger.error(
              `Department with ID ${user.department} not found.`,
            );
            continue;
          }

          const newUser = this.userRepository.create({
            ...user,
            isVerified: true,
            role,
            department,
          });
          await this.userRepository.save(newUser);
        }
      } catch (error) {
        this.logger.error(`Error creating user ${user.email}:`, error.message);
      }
    }
    return users;
  }
}
