import { Injectable } from '@nestjs/common';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import {
  createMap,
  Mapper,
  MappingProfile,
  forMember,
  mapFrom,
} from '@automapper/core';
import { DepartmentDto } from './dto/department.dto';
import { Department } from './entities/department.entity';
import { CreateDepartmentDto } from './dto/create-department.dto';
import { UpdateDepartmentDto } from './dto/update-department.dto';
import { LoggedInUserDepartmentDto } from './dto/logged-in-user-department.dto';

@Injectable()
export class DepartmentMapperService extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile(): MappingProfile {
    return (mapper) => {
      createMap(mapper, Department, DepartmentDto);
      createMap(mapper, DepartmentDto, Department);
      createMap(mapper, CreateDepartmentDto, Department);
      createMap(mapper, UpdateDepartmentDto, Department);
      createMap(mapper, Department, LoggedInUserDepartmentDto);
      createMap(
        mapper,
        Department,
        String,
        forMember(
          (destination) => destination.name,
          mapFrom((source) => source.name), // Map the department name to a string
        ),
      );

      // createMap(
      //   mapper,
      //   User,
      //   UserDto,
      //   forMember(
      //     (destination) => destination.departmentName,
      //     mapFrom((source) => source.department.name), // Map the department name to the departmentName field
      //   ),
      // );
    };
  }
}
