import { Injectable } from '@nestjs/common';
import { UserTableSeeder } from './user-seeder/user.seeder';
import { PermissionTableSeeder } from './permission-seeder/permission.seeder';
import { LoggerService } from '@common/logger/logger.service';

@Injectable()
export class Seeder {
  constructor(
    private readonly logger: LoggerService,
    private readonly userTableSeeder: UserTableSeeder,
    private readonly permissionTableSeeder: PermissionTableSeeder,
  ) {
    this.logger.setContext(Seeder.name);
  }

  async seed() {
    await Promise.all([this.seedUsers(), this.seedPermissions()]);
  }

  private async seedUsers() {
    try {
      const createdStores = await this.userTableSeeder.createStores();
      this.logger.log(`No. of stores created: ${createdStores.length}`);

      const createdDepartments = await this.userTableSeeder.createDepartments();
      this.logger.log(
        `No. of departments created: ${createdDepartments.length}`,
      );

      const createdRoles = await this.userTableSeeder.createRoles();
      this.logger.log(`No. of roles created: ${createdRoles.length}`);

      const createdUsers = await this.userTableSeeder.createUsers();
      this.logger.log(`No. of users created: ${createdUsers.length}`);
      this.logger.log('Successfully completed seeding users...');
    } catch (error) {
      this.logger.error('Failed seeding users...', error);
    }
  }

  private async seedPermissions() {
    try {
      const createdPermissionsCount = await this.permissionTableSeeder.seed();
      this.logger.log(`No. of permissions created: ${createdPermissionsCount}`);
      this.logger.log('Successfully completed seeding permissions...');
    } catch (error) {
      this.logger.error('Failed seeding permissions...', error);
    }
  }
}
