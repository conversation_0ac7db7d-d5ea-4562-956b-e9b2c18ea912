# Package the jar -> semantic versioning -> deploy to docker hub
variables:
  DOCKER_DRIVER: overlay2
  IMAGE_TAG: $CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA
  SCRIPTS_REPO: https://gitlab.com/OduSamuel/ci-scripts.git

before_script:
  - export SCRIPTS_DIR=$(mktemp -d)
  - git clone -q --depth 1 "$SCRIPTS_REPO" "$SCRIPTS_DIR"
    
stages:          # List of stages for jobs, and their order of execution
  - version
  - build
  - deploy-dev
   
   
version:
  image: python:3.7-stretch
  stage: version
  script:
    - mkdir -p ~/.ssh && chmod 700 ~/.ssh
    - ssh-keyscan gitlab.com >> ~/.ssh/known_hosts && chmod 644 ~/.ssh/known_hosts
    - eval $(ssh-agent -s)
    - ssh-add <(echo "$SSH_PRIVATE_KEY")
    - pip install semver
    - $SCRIPTS_DIR/common/gen-semver > version
  artifacts:
    paths:
      - version
  rules:
    - if: $CI_COMMIT_BRANCH == "release"
       
       
docker-build:
  # Use the official docker image.
  image: docker:latest
  stage: build
  services:
    - docker:dind
  before_script:
    - delim="$"
    - docker login -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD" $CI_REGISTRY
  # Default branch leaves tag empty (= latest tag)
  # All other branches are tagged with the escaped branch name (commit ref slug)
  script:
    - export VERSION="unknown"
    - "[ -f ./version ] && export VERSION=$(cat ./version)"
    - docker build --pull -t "$CI_REGISTRY_IMAGE:$VERSION" .
    - docker push "$CI_REGISTRY_IMAGE:$VERSION"
  # Run this job in a branch where a Dockerfile exists
  rules:
    - if: $CI_COMMIT_BRANCH == "release"
      exists:
        - Dockerfile

deploy-dev:
  stage: deploy-dev
  image: line/kubectl-kustomize:1.27.4-5.1.0
  before_script:
    - apk add --no-cache git
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "GitLab CI/CD"
  script:
    - export VERSION="unknown"
    - "[ -f ./version ] && export VERSION=$(cat ./version)"
    - cd ..
    - git clone https://${CD_USERNAME}:${CD_PUSH_TOKEN}@gitlab.com/egfm-devops/kubernetes.git
    - cd kubernetes/egfm-projects/overlays/dev
    - kustomize edit set image $CI_REGISTRY_IMAGE:$VERSION
    - cat kustomization.yaml
    - git add .
    - git commit -m "image update $CI_REGISTRY_IMAGE:$VERSION"
    - git push origin master
  only:
    - release