import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { EntityDto } from '../../../../common/dto/base.dto';

export class AddressDto extends EntityDto {
  @AutoMap()
  @ApiProperty()
  latitude: string;

  @AutoMap()
  @ApiProperty()
  longitude: string;

  @AutoMap()
  @ApiProperty()
  houseAddress: string;

  @AutoMap()
  @ApiProperty()
  country: string;

  @AutoMap()
  @ApiProperty()
  state: string;

  @AutoMap()
  @ApiProperty()
  postalCode: string;
}
