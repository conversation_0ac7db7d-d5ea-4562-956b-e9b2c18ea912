import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { databaseConfig } from '@config/database';
import { CoreModule } from '@core/core.module';
import { SecurityModule } from '@core/security/security.module';
import { StoreModule } from '@core/store/store.module';
import { RequestModule } from '@core/request/request.module';
import { LogModule } from '@core/log/log.module';
import { ComplaintModule } from '@core/complaint/complaint.module';
import { MailerModule, MailerModuleOptions } from 'nestjs-mailer';
import configuration from '@config/index';
import { CloudinaryModule } from '@core/storage/cloudinary/cloudinary.module';
import { JwtAuthGuard } from '@common/guards/jwt-auth.guard';
import { APP_GUARD } from '@nestjs/core';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [configuration],
    }),
    TypeOrmModule.forRoot(databaseConfig),
    MailerModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService): MailerModuleOptions => ({
        config: {
          transport: {
            service: configService.get('MAIL_SERVICE'),
            host: configService.get('MAIL_HOST'),
            port: configService.get('MAIL_PORT'),
            secure: true,
            auth: {
              user: configService.get('MAIL_USERNAME'),
              pass: configService.get('MAIL_PASSWORD'),
            },
          },
          defaults: {
            from: configService.get('MAIL_FROM'),
          },
        },
      }),
      inject: [ConfigService],
    }),
    CoreModule,
    SecurityModule,
    StoreModule,
    RequestModule,
    LogModule,
    ComplaintModule,
    CloudinaryModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard,
    },
    // {
    //   provide: APP_GUARD,
    //   useClass: RolesGuard,
    // },
  ],
})
export class AppModule {}
