import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { EntityDto } from '@common/dto/base.dto';
import { ComplaintSummaryDto } from '@core/complaint/complaint-summary/dto/complaint-summary.dto';

export class ComplaintDto extends EntityDto {
  @AutoMap()
  @ApiProperty()
  complainerName: string;

  @AutoMap()
  @ApiProperty()
  complainerPhone: string;

  @AutoMap()
  @ApiProperty()
  complainerEmail: string;

  @AutoMap()
  @ApiProperty()
  complaintSubject: string;

  @AutoMap()
  @ApiProperty()
  complaintDescription: string;

  @AutoMap()
  @ApiProperty()
  complaintDate: Date;

  @AutoMap()
  @ApiProperty()
  summary: ComplaintSummaryDto;
}
