import { Column, Entity } from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { AbstractEntity } from '@common/entities/base.entity';

@Entity({ name: 'maintenance_log' })
export class MaintenanceLog extends AbstractEntity {
  @AutoMap()
  @Column({ name: 'maintenance_date', type: 'timestamp' })
  maintenanceDate: Date;

  @AutoMap()
  @Column({ name: 'description', type: 'text' })
  description: string;

  @AutoMap()
  @Column({ name: 'cost_of_maintenance', type: 'text' })
  costOfMaintenance: string;

  @AutoMap()
  @Column({
    name: 'serviced_item',
    type: 'varchar',
  })
  servicedItem: string;

  @AutoMap()
  @Column({ name: 'artisan_name', type: 'varchar' })
  artisanName: string;

  @AutoMap()
  @Column({ name: 'artisan_phone', type: 'varchar' })
  artisanPhone: string;

  @AutoMap()
  @Column({ name: 'signature', type: 'varchar', nullable: true })
  signature: string;
}
