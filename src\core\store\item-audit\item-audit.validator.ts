import { ItemAudit } from './entities/item-audit.entity';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DatabaseAction } from '@common/enums/dbaction.enum';
import { BaseValidator } from '@common/validator/base.validator';

@Injectable()
export class ItemAuditValidator implements BaseValidator<ItemAudit> {
  constructor(
    @InjectRepository(ItemAudit)
    private itemAuditRepository: Repository<ItemAudit>,
  ) {}

  async validate(data: ItemAudit, action: DatabaseAction) {
    const existing: ItemAudit = await this.itemAuditRepository.findOne({
      where: { id: data?.id },
    });

    if (DatabaseAction.CREATE === action) {
      // throw new Error('Prayer already exists');
      return;
    } else if (
      existing &&
      existing?.id !== data?.id &&
      DatabaseAction.UPDATE === action
    ) {
      throw new NotFoundException('Prayer not found.');
    }
  }
}
