import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { createMap, Mapper, MappingProfile } from '@automapper/core';
import { Injectable } from '@nestjs/common';
import { MaintenanceLog } from './entities/maintenance-log.entity';
import { MaintenanceLogDto } from './dto/maintenance-log.dto';
import { CreateMaintenanceLogDto } from './dto/create-maintenance-log.dto';

@Injectable()
export class MaintenanceLogMapperService extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile(): MappingProfile {
    return (mapper) => {
      createMap(mapper, MaintenanceLog, MaintenanceLogDto);
      createMap(mapper, MaintenanceLogDto, MaintenanceLog);
      createMap(mapper, CreateMaintenanceLogDto, MaintenanceLog);
    };
  }
}
