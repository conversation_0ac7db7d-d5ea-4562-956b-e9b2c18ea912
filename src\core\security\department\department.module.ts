import { Modu<PERSON> } from '@nestjs/common';
import { DepartmentService } from './department.service';
import { DepartmentController } from './department.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Department } from './entities/department.entity';
import { AutomapperModule } from '@automapper/nestjs';
import { DepartmentValidatorService } from './department.validator.service';
import { DepartmentMapperService } from './department.mapper.service';
import { AuthorizationModule } from '@core/security/authorization/authorization.module';
import { LoggerModule } from '@common/logger/logger.module';

@Module({
  controllers: [DepartmentController],
  exports: [DepartmentService, DepartmentValidatorService],
  imports: [
    TypeOrmModule.forFeature([Department]),
    AutomapperModule,
    AuthorizationModule,
    LoggerModule,
  ],
  providers: [DepartmentService, DepartmentValidatorService, DepartmentMapperService],
})
export class DepartmentModule {}
