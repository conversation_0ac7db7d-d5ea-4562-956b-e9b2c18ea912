import { ApiProperty } from '@nestjs/swagger';
import { AutoMap } from '@automapper/classes';
import { Condition } from '@common/enums/condition.enum';

export class RequestedItemDto {
  @AutoMap()
  @ApiProperty()
  itemId: number;

  @AutoMap()
  @ApiProperty()
  itemName: string;

  @AutoMap()
  @ApiProperty()
  quantityLeased: number;

  @AutoMap(() => String)
  @ApiProperty({
    type: 'string',
    default: Condition.NOT_SPECIFIED,
    enum: Object.values(Condition),
    name: 'conditionBeforeLease',
    description: 'Possible item conditions',
  })
  conditionBeforeLease: Condition;

  @AutoMap()
  @ApiProperty()
  leasedDate: Date;
}
