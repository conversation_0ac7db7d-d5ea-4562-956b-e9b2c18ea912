import { Modu<PERSON> } from '@nestjs/common';
import { RoleService } from './role.service';
import { RoleController } from './role.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AutomapperModule } from '@automapper/nestjs';
import { Role } from './entities/role.entity';
import { LoggerModule } from '../../../common/logger/logger.module';
import { PermissionModule } from '../permission/permission.module';
import { RoleProfile } from './role.profile';

@Module({
  imports: [
    TypeOrmModule.forFeature([Role]),
    AutomapperModule,
    LoggerModule,
    PermissionModule,
  ],
  controllers: [RoleController],
  providers: [RoleService, RoleProfile],
  exports: [RoleService],
})
export class RoleModule {}
