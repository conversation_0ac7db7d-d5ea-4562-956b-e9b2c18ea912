import { Injectable, NotFoundException } from '@nestjs/common';
import { Department } from './entities/department.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DatabaseAction } from '@common/enums/dbaction.enum';
import { BaseValidator } from '@common/validator/base.validator';
import { LoggerService } from '@common/logger/logger.service';

@Injectable()
export class DepartmentValidatorService implements BaseValidator<Department> {
  constructor(
    @InjectRepository(Department)
    private departmentRepository: Repository<Department>,
    private readonly loggerService: LoggerService,
  ) {
    this.loggerService.setContext(DepartmentValidatorService.name);
  }

  async validate(data: Department, action: DatabaseAction) {
    const existing = await this.departmentRepository.findOne({
      where: { id: data?.id },
    });

    if (DatabaseAction.CREATE === action) {
      // throw new Error('Prayer already exists');
      return;
    } else if (
      existing &&
      existing?.id !== data?.id &&
      DatabaseAction.UPDATE === action
    ) {
      throw new NotFoundException('Department not found.');
    }
  }

  async isDepartmentExists(departmentId: number): Promise<boolean> {
    const existing = await this.departmentRepository.findOne({
      where: { id: departmentId },
    });
    return !!existing;
  }
}
