import { Injectable, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  UploadApiErrorResponse,
  UploadApiResponse,
  v2 as cloudinary,
} from 'cloudinary';
import { CloudinaryStorageOption } from '@config/storage';
import { LoggerService } from '@common/logger/logger.service';
import { StorageInterface } from '../storage.interface';

@Injectable()
export class CloudinaryService implements StorageInterface {
  private readonly storageConfig: CloudinaryStorageOption;

  constructor(
    private readonly logger: LoggerService,
    private configService: ConfigService,
  ) {
    this.logger.setContext(CloudinaryService.name);
    this.storageConfig =
      this.configService.get<CloudinaryStorageOption>('storage.cloudinary');
    if (this.storageConfig) {
      cloudinary.config({
        cloud_name: this.storageConfig.cloudName,
        api_key: this.storageConfig.key,
        api_secret: this.storageConfig.secret,
      });
      this.logger.log('Cloudinary configuration initialized.');
    } else {
      this.logger.error('Cloudinary configuration not found.');
      throw new NotFoundException('Cloudinary configuration not found.');
    }
  }

  async deleteFile(publicId: string, signature?: string): Promise<any> {
    return new Promise((resolve, reject) => {
      cloudinary.uploader.destroy(publicId, (error, result) =>
        error ? reject(error) : resolve(result),
      );
    });
  }

  async uploadFile(
    file?: Buffer,
    hash?: string,
    folder?: string,
    extension?: string,
  ): Promise<UploadApiResponse | UploadApiErrorResponse> {
    // Validate required parameters
    if (!file) {
      throw new Error('File buffer is required');
    }

    if (!folder) {
      throw new Error('Folder is required');
    }

    // Determine MIME type based on extension
    const mimeType = this.getMimeType(extension || 'pdf');
    const resourceType = this.getResourceType(extension);

    this.logger.log(
      `Uploading file to Cloudinary: folder=${folder}, extension=${extension}, mimeType=${mimeType}, resourceType=${resourceType}`,
    );

    return new Promise((resolve, reject) => {
      cloudinary.uploader.upload(
        `data:${mimeType};base64,${file.toString('base64')}`,
        {
          folder: folder,
          unique_filename: true,
          format: extension,
          resource_type: resourceType,
        },
        (error, result) => {
          if (error) {
            this.logger.error(`Cloudinary upload failed: ${error.message}`);
            reject(error);
          } else {
            this.logger.log(`File uploaded successfully: ${result.public_id}`);
            resolve(result);
          }
        },
      );
    });
  }

  /**
   * Get MIME type based on file extension
   */
  private getMimeType(extension: string): string {
    const mimeTypes: { [key: string]: string } = {
      pdf: 'application/pdf',
      doc: 'application/msword',
      docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      jpg: 'image/jpeg',
      jpeg: 'image/jpeg',
      png: 'image/png',
      gif: 'image/gif',
      webp: 'image/webp',
      svg: 'image/svg+xml',
    };

    return mimeTypes[extension.toLowerCase()] || 'application/octet-stream';
  }

  /**
   * Get Cloudinary resource type based on file extension
   */
  private getResourceType(extension?: string): 'image' | 'video' | 'raw' {
    if (!extension) return 'raw';

    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];
    const videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv'];

    const ext = extension.toLowerCase();

    if (imageExtensions.includes(ext)) return 'image';
    if (videoExtensions.includes(ext)) return 'video';
    return 'raw'; // For documents, PDFs, etc.
  }
}
