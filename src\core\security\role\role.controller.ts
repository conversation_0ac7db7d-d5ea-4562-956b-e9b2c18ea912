import {
  Body,
  Controller,
  DefaultValuePipe,
  Get,
  HttpStatus,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Req,
  Res,
} from '@nestjs/common';
import { RoleService } from './role.service';
import { RoleDto } from './dto/role.dto';
import {
  ApiBody,
  ApiExcludeEndpoint,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { Role } from './entities/role.entity';
import { Permission } from '../permission/entities/permission.entity';
import { RolePermissionDto } from './dto/role-permission.dto';
import { Pagination } from 'nestjs-typeorm-paginate';
import { ILike } from 'typeorm';
import { PermissionService } from '../permission/permission.service';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { CreateRoleDto } from './dto/create-role.dto';
import { CoreUtils } from '../../../common/utils/core.utils';
import { AddPermissionDto } from './dto/add-permission.dto';
import { result } from 'lodash';

@ApiTags('Role')
@Controller({
  path: 'role',
  version: '1',
})
export class RoleController {
  constructor(
    private readonly roleService: RoleService,
    private readonly permissionService: PermissionService,
    @InjectMapper() private readonly classMapper: Mapper,
  ) {}

  @ApiOperation({ summary: 'Get all roles' })
  @Get()
  async index(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page = 1,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit = 10,
    @Query('search') search: string,
    @Query('filter') filter: string,
    @Req() request: any,
    @Res() response: any,
  ) {
    limit = limit > 100 ? 100 : limit; // limit the pagination to 100
    const where = {};
    if (search) {
      where['name'] = ILike(`%${search}%`);
    }
    if (filter) {
      where['status'] = filter;
    }
    const pagination = await this.roleService.paginate(
      {
        page,
        limit,
        route: `/api/role`,
      },
      where,
    );
    const dtoList = await this.classMapper.mapArrayAsync(
      pagination.items,
      Role,
      RoleDto,
    );

    response
      .status(HttpStatus.OK)
      .json(new Pagination(dtoList, pagination.meta, pagination.links));
  }

  @ApiOperation({ summary: 'Get all roles' })
  @ApiExcludeEndpoint()
  @Get('available')
  async availableRoles(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page = 1,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit = 10,
    @Query('search') search: string,
    @Query('user_id') userId: number,
    @Req() request: any,
    @Res() response: any,
  ) {
    limit = limit > 100 ? 100 : limit; // limit the pagination to 100
    const pagination = await this.roleService.availableRoles(
      userId,
      page,
      limit,
      search,
    );
    const dtoList = pagination.items.map((result) => {
      const dto: RoleDto = new RoleDto();
      dto.id = result['role_id'];
      dto.name = result['role_name'];
      dto.description = result['role_description'];
      dto.status = result['role_status'];
      dto.createdAt = result['role_created_at'];
      dto.updatedAt = result['role_updated_at'];
      return dto;
    });
    response
      .status(HttpStatus.OK)
      .json(new Pagination(dtoList, pagination.meta, pagination.links));
  }

  @ApiOperation({ summary: 'Get all roles' })
  @ApiExcludeEndpoint()
  @Get('assigned')
  async assignedRoles(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page = 1,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit = 10,
    @Query('search') search: string,
    @Query('user_id') userId: number,
    @Req() request: any,
    @Res() response: any,
  ) {
    limit = limit > 100 ? 100 : limit; // limit the pagination to 100
    const pagination = await this.roleService.assignedRoles(
      userId,
      page,
      limit,
      search,
    );
    const dtoList = pagination.items.map((result) => {
      const dto: RoleDto = new RoleDto();
      dto.id = result['role_id'];
      dto.name = result['role_name'];
      dto.description = result['role_description'];
      dto.status = result['role_status'];
      dto.createdAt = result['role_created_at'];
      dto.updatedAt = result['role_updated_at'];
      return dto;
    });
    response
      .status(HttpStatus.OK)
      .json(new Pagination(dtoList, pagination.meta, pagination.links));
  }

  @ApiOperation({ summary: 'Make role(s) active' })
  @Post('activate')
  async activate(
    @Req() request: any,
    @Res() response: any,
    @Body() ids: Array<number>,
  ) {
    try {
      await this.roleService.activate(ids);
      return response.status(HttpStatus.OK).json({
        statusCode: HttpStatus.OK,
        message: 'Roles activated successfully',
      });
    } catch (err) {
      return response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: `Internal server error: ${err.message}`,
      });
    }
  }

  @ApiOperation({ summary: 'Make role(s) inactive' })
  @Post('deactivate')
  async deactivate(
    @Req() request: any,
    @Res() response: any,
    @Body() ids: Array<number>,
  ) {
    try {
      await this.roleService.deactivate(ids);
      return response.status(HttpStatus.OK).json({
        statusCode: HttpStatus.OK,
        message: 'Roles deactivated successfully',
      });
    } catch (err) {
      return response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: `Internal server error: ${err.message}`,
      });
    }
  }

  @ApiOperation({ summary: 'Add permission to a role' })
  @ApiBody({ type: AddPermissionDto })
  @Post('add-permission/:roleId')
  async addPermission(
    @Param('roleId') roleId: number,
    @Req() request: any,
    @Res() response: any,
    @Body() dto: AddPermissionDto,
  ) {
    try {
      // find a role by dto.id
      const role: Role = await this.roleService.findByPk(roleId);
      if (role) {
        // From the dto object, get the permission ids and find the permissions
        const results: Array<Permission> =
          await this.permissionService.findByIds(dto.permissionIds);

        // Add the permissions to the role
        role.permissions = role.permissions.concat(results);
        await this.roleService.update(role);

        return CoreUtils.genericResponse(
          response,
          HttpStatus.OK,
          'Permissions added successfully',
        );
      } else {
        return CoreUtils.genericResponse(
          response,
          HttpStatus.NOT_FOUND,
          'Role not found',
        );
      }
    } catch (err) {
      return CoreUtils.genericResponse(
        response,
        HttpStatus.INTERNAL_SERVER_ERROR,
        `Internal server error: ${err.message}`,
      );
    }
  }

  @ApiOperation({ summary: 'Remove permission from a role' })
  @ApiBody({ type: RolePermissionDto })
  @Post('remove-permission')
  async removePermission(
    @Req() request: any,
    @Res() response: any,
    @Body() dto: RolePermissionDto,
  ) {
    try {
      const role: Role = await this.roleService.findByPk(dto.id);
      role.permissions = role.permissions.filter(
        (val) => !dto.permissions.includes(val.id),
      );
      await this.roleService.update(role);
      return response.status(HttpStatus.OK).json(dto);
    } catch (err) {
      return response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: `Internal server error: ${err.message}`,
      });
    }
  }

  @ApiOperation({ summary: 'Add all permissions to a role' })
  @Post('add-all-permissions')
  async addAllPermissions(
    @Req() request: any,
    @Res() response: any,
    @Body() body: any,
  ) {
    try {
      const role: Role = await this.roleService.findByPk(body.roleId);
      const availablePermissions: Array<Permission> =
        await this.permissionService.findAllAvailablePermissions(body.roleId);
      role.permissions = role.permissions.concat(availablePermissions);
      await this.roleService.update(role);
      return response.status(HttpStatus.OK).json(body);
    } catch (err) {
      return response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: `Internal server error: ${err.message}`,
      });
    }
  }

  @ApiOperation({ summary: 'Remove all permissions from a role' })
  @Post('remove-all-permissions')
  async removeAllPermissions(
    @Req() request: any,
    @Res() response: any,
    @Body() body: any,
  ) {
    try {
      const role: Role = await this.roleService.findByPk(body.roleId);
      role.permissions.length = 0;
      await this.roleService.update(role);
      response.status(HttpStatus.OK).json(body);
    } catch (err) {
      return response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: `Internal server error: ${err.message}`,
      });
    }
  }

  @ApiOperation({ summary: 'Create a new role' })
  @Post('new')
  @ApiBody({ type: CreateRoleDto })
  async create(
    @Body() createRoleDto: CreateRoleDto,
    @Res() response: any,
    @Req() request: any,
  ) {
    try {
      const role = await this.classMapper.mapAsync(
        createRoleDto,
        CreateRoleDto,
        Role,
      );
      role.createdBy =
        `${request?.user?.firstName} ${request?.user?.lastName}` ?? 'System';
      await this.roleService.create(role);

      return response.status(HttpStatus.CREATED).json({
        statusCode: HttpStatus.CREATED,
        message: 'Role created successfully',
      });
    } catch (err) {
      return response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: `Internal server error: ${err.message}`,
      });
    }
  }

  @ApiOperation({ summary: 'Get all roles' })
  @Get('all')
  async findAll(@Res() response: any) {
    try {
      const roles = await this.roleService.findAll();
      return CoreUtils.genericResponse(
        response,
        HttpStatus.OK,
        'Roles retrieved successfully',
        await this.classMapper.mapArrayAsync(roles, Role, RoleDto),
      );
    } catch (err) {
      return response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: `Internal server error: ${err.message}`,
      });
    }
  }

  @ApiOperation({ summary: 'Get a role by id' })
  @Get(':id')
  async findOne(@Param('id') id: number, @Res() response: any) {
    try {
      return response.status(HttpStatus.OK).json({
        statusCode: HttpStatus.OK,
        message: await this.classMapper.mapAsync(
          await this.roleService.findByPk(id),
          Role,
          RoleDto,
        ),
      });
    } catch (err) {
      return response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: `Internal server error: ${err.message}`,
      });
    }
  }

  @ApiOperation({ summary: 'Update a role' })
  @ApiBody({ type: RoleDto })
  @Patch(':id')
  async update(
    @Param('id') id: number,
    @Body() updateRoleDto: RoleDto,
    @Res() response: any,
    @Req() request: any,
  ) {
    try {
      const role = await this.roleService.findByPk(id);
      await this.classMapper.mutateAsync(updateRoleDto, role, RoleDto, Role);
      role.updatedBy =
        `${request?.user?.firstName} ${request?.user?.lastName}` ?? 'System';
      await this.roleService.update(role);
      return response.status(HttpStatus.OK).json({
        statusCode: HttpStatus.OK,
        message: 'Role updated successfully',
      });
    } catch (err) {
      return response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: `Internal server error: ${err.message}`,
      });
    }
  }

  @ApiOperation({ summary: 'Delete a role' })
  @Post('delete-selection')
  async removeSelection(
    @Req() request: any,
    @Res() response: any,
    @Body() ids: Array<number>,
  ) {
    try {
      ids.map((id) => {
        this.roleService.remove(id);
      });
      return response.status(HttpStatus.OK).json({ roleIds: ids });
    } catch (err) {
      return response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: `Internal server error: ${err.message}`,
      });
    }
  }
}
