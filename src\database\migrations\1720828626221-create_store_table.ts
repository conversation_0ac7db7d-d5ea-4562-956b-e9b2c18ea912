import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateStoreTable1720828626221 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create store table
    await queryRunner.createTable(
      new Table({
        name: 'store',
        columns: [
          {
            name: 'id',
            type: 'bigint',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'name',
            type: 'varchar',
          },
          {
            name: 'status',
            type: 'varchar',
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'now()',
          },
          {
            name: 'created_by',
            type: 'varchar',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'updated_by',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'deleted_at',
            type: 'timestamp',
            isNullable: true,
          },
        ],
      }),
      true,
    );

    // Add an index to the store table.
    await queryRunner.createIndex(
      'store',
      new TableIndex({
        name: 'IDX_STORE_NAME',
        columnNames: ['name'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop index
    await queryRunner.dropIndex('store', 'IDX_STORE_NAME');
    // Drop store table
    await queryRunner.dropTable('store');
  }
}
