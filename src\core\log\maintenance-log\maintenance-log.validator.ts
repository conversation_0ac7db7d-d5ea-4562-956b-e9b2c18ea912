import { Injectable, NotFoundException } from '@nestjs/common';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { BaseValidator } from '@common/validator/base.validator';
import { MaintenanceLog } from '@core/log/maintenance-log/entities/maintenance-log.entity';
import { DatabaseAction } from '@common/enums/dbaction.enum';

@Injectable()
export class MaintenanceLogValidator implements BaseValidator<MaintenanceLog> {
  constructor(
    @InjectRepository(MaintenanceLog)
    private maintenanceLogRepository: Repository<MaintenanceLog>,
  ) {}

  async validate(data: MaintenanceLog, action: DatabaseAction) {
    const existing: MaintenanceLog =
      await this.maintenanceLogRepository.findOne({
        where: { id: data?.id },
      });

    if (DatabaseAction.CREATE === action) {
      return;
    } else if (existing && existing?.id !== data?.id) {
      if (DatabaseAction.UPDATE === action) {
        throw new NotFoundException('Maintenance log not found.');
      }
    }
  }
}
