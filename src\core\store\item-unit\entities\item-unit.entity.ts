import { AutoMap } from '@automapper/classes';
import { AbstractEntity } from '@common/entities/base.entity';
import { Condition } from '@common/enums/condition.enum';
import { Store } from '@core/store/entities/store.entity';
import { Item } from '@core/store/item/entities/item.entity';
import { Column, Entity, JoinColumn, ManyToOne } from 'typeorm';

@Entity({ name: 'item_unit' })
export class ItemUnit extends AbstractEntity {
  @AutoMap()
  @Column({ name: 'serial_number', type: 'varchar', unique: true })
  serialNumber: string;

  @AutoMap(() => String)
  @Column({
    name: 'condition',
    type: 'enum',
    enum: Condition,
    default: Condition.NOT_SPECIFIED,
  })
  condition: Condition;

  @AutoMap()
  @ManyToOne(() => Item, (item) => item.itemUnits, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'item_id' })
  item: Item;

  @AutoMap(() => Store)
  @ManyToOne(() => Store, (store) => store.itemUnits, { eager: true })
  @JoinColumn({ name: 'store_id' })
  store: Store;
}
