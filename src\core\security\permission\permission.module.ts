import { Module } from '@nestjs/common';
import { PermissionService } from './permission.service';
import { PermissionController } from './permission.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Permission } from './entities/permission.entity';
import { AutomapperModule } from '@automapper/nestjs';
import { classes } from '@automapper/classes';
import { PermissionProfile } from './permission.profile';
import { LoggerModule } from '../../../common/logger/logger.module';

@Module({
  controllers: [PermissionController],
  exports: [PermissionService],
  imports: [
    TypeOrmModule.forFeature([Permission]),
    AutomapperModule.forRoot({
      strategyInitializer: classes(),
    }),
    LoggerModule,
  ],
  providers: [PermissionService, PermissionProfile],
})
export class PermissionModule {}
