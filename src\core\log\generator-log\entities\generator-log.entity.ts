import { Column, Entity } from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { AbstractEntity } from '@common/entities/base.entity';

@Entity({ name: 'generator_log' })
export class GeneratorLog extends AbstractEntity {
  @AutoMap()
  @Column({
    name: 'name_of_meeting',
    type: 'varchar',
  })
  nameOfMeeting: string;

  @AutoMap()
  @Column({ name: 'generator_type', type: 'varchar' })
  generatorType: string;

  @AutoMap()
  @Column({ name: 'meeting_location', type: 'varchar' })
  meetingLocation: string;

  @AutoMap()
  @Column({ name: 'on_time', type: 'timestamp' })
  onTime: Date;

  @AutoMap()
  @Column({ name: 'off_time', type: 'timestamp' })
  offTime: Date;

  @AutoMap()
  @Column({ name: 'hours_used', type: 'int' })
  hoursUsed: number;

  @AutoMap()
  @Column({ name: 'engine_start_hours', type: 'varchar' })
  engineStartHours: string;

  @AutoMap()
  @Column({ name: 'engine_off_hours', type: 'varchar' })
  engineOffHours: string;

  @AutoMap()
  @Column({ name: 'diesel_level_on', type: 'varchar' })
  dieselLevelOn: string;

  @AutoMap()
  @Column({ name: 'diesel_level_off', type: 'varchar' })
  dieselLevelOff: string;

  @AutoMap()
  @Column({ name: 'last_service_hour', type: 'varchar' })
  lastServiceHour: string;

  @AutoMap()
  @Column({ name: 'next_service_hour', type: 'varchar' })
  nextServiceHour: string;

  @AutoMap()
  @Column({ name: 'due_for_service', type: 'boolean' })
  dueForService: boolean;

  @AutoMap()
  @Column({ name: 'oil_filter_due_for_replacement', type: 'boolean' })
  oilFilterDueForReplacement: boolean;

  @AutoMap()
  @Column({
    name: 'last_oil_filter_replacement',
    type: 'timestamp',
    nullable: true,
  })
  lastOilFilterReplacement: Date;

  @AutoMap()
  @Column({ name: 'fault_detected', type: 'boolean' })
  faultDetected: boolean;

  @AutoMap()
  @Column({ name: 'fault_description', type: 'text', nullable: true })
  faultDescription: string;

  @AutoMap()
  @Column({ name: 'personnel_name', type: 'varchar' })
  personnelName: string;

  @AutoMap()
  @Column({ name: 'remark', type: 'text' })
  Remark: string;
}
