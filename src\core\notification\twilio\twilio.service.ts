import {
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { TwilioConfig } from 'src/config/twilio';
import { ConfigService } from '@nestjs/config';
import { LoggerService } from '../../../common/logger/logger.service';
import { Twilio } from 'twilio';

export interface TwilioMessage {
  From: string;
  Body: string;
}

@Injectable() // 👈 Add this decorator
export class TwilioService {
  private readonly twilioCredentials: TwilioConfig;
  private client: Twilio;
  constructor(
    private readonly configService: ConfigService,
    private readonly logger: LoggerService,
  ) {
    this.logger.setContext(TwilioService.name);
    this.twilioCredentials = this.configService.get<TwilioConfig>('twilio');
    if (this.twilioCredentials) {
      this.client = new Twilio(
        this.twilioCredentials.accountSid,
        this.twilioCredentials.authToken,
      );
      this.logger.log('Twilio configuration initialized.');
    } else {
      this.logger.error('Twilio configuration not found.');
      throw new NotFoundException('Twilio configuration not found.');
    }
  }

  /**
   * Send a WhatsApp message to the specified number.
   * @param to
   * @param message
   */
  async sendWhatsAppMessage(to: string, message: string): Promise<any> {
    try {
      return await this.client.messages.create({
        from: `whatsapp:${this.twilioCredentials.sandboxNumber}`,
        to: `whatsapp:${to}`,
        body: message,
      });
    } catch (err) {
      this.logger.error(`Failed to send message: ${err.message}`);
      throw new InternalServerErrorException(
        `Failed to send message: ${err.message}`,
      );
    }
  }

  /**
   * Send an SMS message to the specified number.
   * @param to
   * @param message
   */
  async sendSMSMessage(to: string, message: string) {
    try {
      return await this.client.messages.create({
        from: this.twilioCredentials.sandboxNumber as string,
        to,
        body: message,
      });
    } catch (err) {
      this.logger.error(`Failed to send message: ${err.message}`);
      throw new InternalServerErrorException(
        `Failed to send message: ${err.message}`,
      );
    }
  }

  // TODO: Complete this method when you have a clear understanding of what you want to use the received message to do.
  async handleIncomingMessage(message: TwilioMessage) {
    this.logger.log(`Received message from ${message.From}: ${message.Body}`);
    // Process the message as needed
  }
}
