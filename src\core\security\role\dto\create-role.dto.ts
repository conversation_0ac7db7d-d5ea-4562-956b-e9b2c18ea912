import { AutoMap } from '@automapper/classes';
import { IsNotEmpty, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { PermissionDto } from '../../permission/dto/permission.dto';

export class CreateRoleDto {
  @AutoMap()
  @IsNotEmpty()
  @IsString()
  @ApiProperty()
  name: string;

  @AutoMap()
  @IsNotEmpty()
  @IsString()
  @ApiProperty()
  description?: string;

  @AutoMap(() => PermissionDto)
  @ApiProperty({ type: PermissionDto })
  permissions?: Array<PermissionDto>;
}
