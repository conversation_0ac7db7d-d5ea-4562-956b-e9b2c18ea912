import { Test, TestingModule } from '@nestjs/testing';
import { ItemAuditController } from './item-audit.controller';
import { ItemAuditService } from './item-audit.service';

describe('ItemAuditController', () => {
  let controller: ItemAuditController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ItemAuditController],
      providers: [ItemAuditService],
    }).compile();

    controller = module.get<ItemAuditController>(ItemAuditController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
