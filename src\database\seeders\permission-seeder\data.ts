import { PermissionDto } from '../../../core/security/permission/dto/permission.dto';
import { UserActions } from '../../../common/enums/user-actions.enum';

export const permissions: Array<PermissionDto> = [
  { name: UserActions.CREATE, description: 'Can Create Record' },
  { name: UserActions.DELETE, description: 'Can Delete Record' },
  { name: UserActions.READ, description: 'Can Read Record' },
  { name: UserActions.MANAGE, description: 'Can Manage Record' },
  { name: UserActions.UPDATE, description: 'Can Modify Record' },
];
