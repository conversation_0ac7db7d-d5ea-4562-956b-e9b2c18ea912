import {
  MigrationInterface,
  QueryRunner,
  <PERSON><PERSON><PERSON>umn,
  TableForeign<PERSON>ey,
} from 'typeorm';

export class CreateItemAuditRequestAuditFkTable1752660688370
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'item_audit',
      new TableColumn({
        name: 'audit_id',
        type: 'bigint',
      }),
    );

    await queryRunner.createForeignKey(
      'item_audit',
      new TableForeignKey({
        columnNames: ['audit_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'request_audit',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const table = await queryRunner.getTable('item_audit');
    const foreignKey = table.foreignKeys.find(
      (fk) => fk.columnNames.indexOf('request_id') !== -1,
    );
    await queryRunner.dropForeignKey('request_id', foreignKey);
    await queryRunner.dropColumn('item_audit', 'request_id');
  }
}
