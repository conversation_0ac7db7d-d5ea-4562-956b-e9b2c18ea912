import { Test, TestingModule } from '@nestjs/testing';
import { ComplaintSummaryService } from './complaint-summary.service';

describe('CompliantAuditService', () => {
  let service: ComplaintSummaryService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ComplaintSummaryService],
    }).compile();

    service = module.get<ComplaintSummaryService>(ComplaintSummaryService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
