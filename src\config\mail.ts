import { Options, TransportType } from 'nestjs-mailer';
import { config } from 'dotenv';

config();

export interface MailerConfig {
  transport?: TransportType;
  defaults?: Options;
}

export const mailerConfig = (): MailerConfig => ({
  transport: {
    service: process.env.MAIL_SERVICE,
    host: process.env.MAIL_HOST,
    port: parseInt(process.env.MAIL_PORT, 10) || 587,
    secure: true,
    auth: {
      user: process.env.MAIL_USERNAME,
      pass: process.env.MAIL_PASSWORD,
    },
  },
  defaults: {
    from: process.env.MAIL_FROM,
  },
});
