import { PaginatedResponseDto } from '@common/dto/paginated-response.dto';
import { PaginationDto } from '@common/dto/pagination.dto';

export interface IEntityServiceStrategy<T> {
  create(data: T): Promise<T | void>;
  update(data: T): Promise<T | void>;
  findByPk(id: number): Promise<T | null>;
  activate(ids: number[]): Promise<void>;
  deactivate(ids: number[]): Promise<void>;
  table?<R = T>(
    paginationDto: PaginationDto,
    ...args: Array<string | number>
  ): Promise<PaginatedResponseDto<R>>;
}
