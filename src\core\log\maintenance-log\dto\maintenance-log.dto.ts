import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { EntityDto } from '@common/dto/base.dto';

export class MaintenanceLogDto extends EntityDto {
  @AutoMap()
  @ApiProperty()
  maintenanceDate: Date;

  @AutoMap()
  @ApiProperty()
  description: string;

  @AutoMap()
  @ApiProperty()
  costOfMaintenance: number;

  @AutoMap()
  @ApiProperty()
  servicedItem: string;

  @AutoMap()
  @ApiProperty()
  artisanName: string;

  @AutoMap()
  @ApiProperty()
  artisanPhone: string;

  @AutoMap()
  @ApiProperty()
  signature: string;
}
