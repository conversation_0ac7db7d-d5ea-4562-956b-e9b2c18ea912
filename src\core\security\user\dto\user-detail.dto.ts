import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { Gender } from '@common/enums/gender.enum';
import { EntityDto } from '@common/dto/base.dto';
import { DepartmentDto } from '@core/security/department/dto/department.dto';
import { RoleDto } from '@core/security/role/dto/role.dto';
import { AddressDto } from '@core/security/address/dto/address.dto';

export class UserDetailDto extends EntityDto {
  @AutoMap()
  @ApiProperty()
  email: string;

  @AutoMap()
  @ApiProperty()
  firstName?: string;

  @AutoMap()
  @ApiProperty()
  lastName?: string;

  @AutoMap()
  @ApiProperty()
  phoneNumber?: string;

  @AutoMap()
  @ApiProperty({
    type: 'string',
    enum: Object.values(Gender),
    name: 'gender',
    description: 'Possible genders',
    default: Gender.DEFAULT,
  })
  gender: Gender;

  @AutoMap()
  @ApiProperty()
  department: DepartmentDto;

  @AutoMap()
  @ApiProperty()
  role: RoleDto;

  @AutoMap()
  @ApiProperty()
  address?: AddressDto;
}
